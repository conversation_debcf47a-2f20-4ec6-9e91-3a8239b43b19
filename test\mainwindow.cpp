#include "mainwindow.h"
#include "taskpage.h"
#include "taskmanager.h"
#include <QApplication>
#include <QDateTime>
#include <QSettings>
#include <QScreen>
#include <QFile>
#include <QMessageBox>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mainLayout(nullptr)
    , m_navigationLayout(nullptr)
    , m_stackedWidget(nullptr)
    , m_previousButton(nullptr)
    , m_nextButton(nullptr)
    , m_pageIndicator(nullptr)
    , m_progressBar(nullptr)
    , m_menuBar(nullptr)
    , m_statusBar(nullptr)
    , m_statusLabel(nullptr)
    , m_timeLabel(nullptr)
    , m_aboutAction(nullptr)
    , m_helpAction(nullptr)
    , m_settingsAction(nullptr)
    , m_exitAction(nullptr)
    , m_taskManager(nullptr)
    , m_currentPageIndex(0)
    , m_totalPages(0)
    , m_statusTimer(nullptr)
{
    setupUI();
    connectSignals();
    loadTasks();
    restoreWindowState();
    
    // 启动状态更新定时器
    m_statusTimer = new QTimer(this);
    connect(m_statusTimer, &QTimer::timeout, this, &MainWindow::updateStatusBar);
    m_statusTimer->start(1000); // 每秒更新一次
    
    qDebug() << "MainWindow initialized successfully";
}

MainWindow::~MainWindow()
{
    saveWindowState();
    qDebug() << "MainWindow destroyed";
}

void MainWindow::setupUI()
{
    // 设置窗口属性
    setWindowTitle("药盒识别与用药建议系统 v1.0");
    setMinimumSize(WINDOW_MIN_WIDTH, WINDOW_MIN_HEIGHT);
    resize(1280, 800);
    
    // 设置窗口居中
    QScreen *screen = QApplication::primaryScreen();
    if (screen) {
        QRect screenGeometry = screen->geometry();
        int x = (screenGeometry.width() - width()) / 2;
        int y = (screenGeometry.height() - height()) / 2;
        move(x, y);
    }
    
    setupMenuBar();
    setupCentralWidget();
    setupStatusBar();
}

void MainWindow::setupMenuBar()
{
    m_menuBar = menuBar();
    
    // 文件菜单
    QMenu *fileMenu = m_menuBar->addMenu("文件(&F)");
    
    m_settingsAction = new QAction("设置(&S)", this);
    m_settingsAction->setShortcut(QKeySequence::Preferences);
    m_settingsAction->setStatusTip("打开设置对话框");
    fileMenu->addAction(m_settingsAction);
    
    fileMenu->addSeparator();
    
    m_exitAction = new QAction("退出(&X)", this);
    m_exitAction->setShortcut(QKeySequence::Quit);
    m_exitAction->setStatusTip("退出应用程序");
    fileMenu->addAction(m_exitAction);
    
    // 帮助菜单
    QMenu *helpMenu = m_menuBar->addMenu("帮助(&H)");
    
    m_helpAction = new QAction("使用帮助(&H)", this);
    m_helpAction->setShortcut(QKeySequence::HelpContents);
    m_helpAction->setStatusTip("显示使用帮助");
    helpMenu->addAction(m_helpAction);
    
    helpMenu->addSeparator();
    
    m_aboutAction = new QAction("关于(&A)", this);
    m_aboutAction->setStatusTip("关于本应用程序");
    helpMenu->addAction(m_aboutAction);
}

void MainWindow::setupCentralWidget()
{
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    m_mainLayout->setSpacing(10);
    
    setupNavigationBar();
    
    // 创建堆叠窗口部件
    m_stackedWidget = new QStackedWidget(this);
    m_mainLayout->addWidget(m_stackedWidget, 1);
    
    // 创建任务管理器
    m_taskManager = new TaskManager(this);
}

void MainWindow::setupNavigationBar()
{
    // 创建导航栏布局
    m_navigationLayout = new QHBoxLayout();
    m_navigationLayout->setContentsMargins(0, 0, 0, 0);
    m_navigationLayout->setSpacing(10);
    
    // 上一页按钮
    m_previousButton = new QPushButton("◀ 上一页", this);
    m_previousButton->setFixedSize(NAVIGATION_BUTTON_WIDTH, NAVIGATION_BUTTON_HEIGHT);
    m_previousButton->setEnabled(false);
    m_navigationLayout->addWidget(m_previousButton);
    
    // 页面指示器
    m_pageIndicator = new QLabel("第 1 页，共 1 页", this);
    m_pageIndicator->setAlignment(Qt::AlignCenter);
    m_pageIndicator->setObjectName("titleLabel");
    m_navigationLayout->addWidget(m_pageIndicator, 1);
    
    // 下一页按钮
    m_nextButton = new QPushButton("下一页 ▶", this);
    m_nextButton->setFixedSize(NAVIGATION_BUTTON_WIDTH, NAVIGATION_BUTTON_HEIGHT);
    m_nextButton->setEnabled(false);
    m_navigationLayout->addWidget(m_nextButton);
    
    // 进度条
    m_progressBar = new QProgressBar(this);
    m_progressBar->setFixedHeight(6);
    m_progressBar->setTextVisible(false);
    m_progressBar->setMinimum(0);
    m_progressBar->setMaximum(100);
    m_progressBar->setValue(0);
    
    // 添加到主布局
    m_mainLayout->addLayout(m_navigationLayout);
    m_mainLayout->addWidget(m_progressBar);
}

void MainWindow::setupStatusBar()
{
    m_statusBar = statusBar();
    
    m_statusLabel = new QLabel("就绪", this);
    m_statusBar->addWidget(m_statusLabel, 1);
    
    m_timeLabel = new QLabel(this);
    m_statusBar->addPermanentWidget(m_timeLabel);
    
    updateStatusBar();
}

void MainWindow::connectSignals()
{
    // 导航按钮信号
    connect(m_previousButton, &QPushButton::clicked, this, &MainWindow::onPreviousPage);
    connect(m_nextButton, &QPushButton::clicked, this, &MainWindow::onNextPage);
    
    // 菜单动作信号
    connect(m_aboutAction, &QAction::triggered, this, &MainWindow::onAboutApplication);
    connect(m_helpAction, &QAction::triggered, this, &MainWindow::onShowHelp);
    connect(m_settingsAction, &QAction::triggered, this, &MainWindow::onShowSettings);
    connect(m_exitAction, &QAction::triggered, this, &QWidget::close);
}

void MainWindow::loadTasks()
{
    // 创建单个任务页面
    TaskPage *page = new TaskPage(this);
    m_taskPages.append(page);
    m_stackedWidget->addWidget(page);

    // 加载题目数据 - 尝试多个可能的路径
    QStringList possiblePaths = {
        QApplication::applicationDirPath() + "/config/questions.json",
        "./config/questions.json",
        "../config/questions.json",
        "config/questions.json"
    };

    QString questionsPath;
    for (const QString& path : possiblePaths) {
        if (QFile::exists(path)) {
            questionsPath = path;
            break;
        }
    }

    if (questionsPath.isEmpty()) {
        qWarning() << "Cannot find questions.json file in any of the expected locations";
        QMessageBox::warning(this, "错误", "找不到题目文件 questions.json");
    } else {
        qDebug() << "Found questions file at:" << questionsPath;
        page->loadQuestions(questionsPath);
    }

    // 连接任务页面信号
    connect(page, &TaskPage::questionAnswered, this, &MainWindow::onQuestionAnswered);
    connect(page, &TaskPage::taskError, this, &MainWindow::onTaskError);
    connect(page, &TaskPage::questionChanged, this, &MainWindow::onQuestionChanged);

    m_totalPages = 1;

    // 更新UI状态
    updateNavigationButtons();
    updatePageIndicator();

    qDebug() << "Task page loaded with questions from:" << questionsPath;
}

void MainWindow::onPreviousPage()
{
    // 如果只有一个页面，则控制题目导航
    if (m_totalPages == 1 && !m_taskPages.isEmpty()) {
        TaskPage* currentPage = m_taskPages[m_currentPageIndex];
        if (currentPage && currentPage->hasPreviousQuestion()) {
            currentPage->goToPreviousQuestion();
            m_statusLabel->setText("切换到上一题");
        }
    } else if (m_currentPageIndex > 0) {
        m_currentPageIndex--;
        m_stackedWidget->setCurrentIndex(m_currentPageIndex);
        updateNavigationButtons();
        updatePageIndicator();

        m_statusLabel->setText(QString("切换到第 %1 页").arg(m_currentPageIndex + 1));
    }
}

void MainWindow::onNextPage()
{
    // 如果只有一个页面，则控制题目导航
    if (m_totalPages == 1 && !m_taskPages.isEmpty()) {
        TaskPage* currentPage = m_taskPages[m_currentPageIndex];
        if (currentPage && currentPage->hasNextQuestion()) {
            currentPage->goToNextQuestion();
            m_statusLabel->setText("切换到下一题");
        }
    } else if (m_currentPageIndex < m_totalPages - 1) {
        m_currentPageIndex++;
        m_stackedWidget->setCurrentIndex(m_currentPageIndex);
        updateNavigationButtons();
        updatePageIndicator();

        m_statusLabel->setText(QString("切换到第 %1 页").arg(m_currentPageIndex + 1));
    }
}

void MainWindow::onGoToPage(int pageIndex)
{
    if (pageIndex >= 0 && pageIndex < m_totalPages) {
        m_currentPageIndex = pageIndex;
        m_stackedWidget->setCurrentIndex(m_currentPageIndex);
        updateNavigationButtons();
        updatePageIndicator();
        
        m_statusLabel->setText(QString("跳转到第 %1 页").arg(m_currentPageIndex + 1));
    }
}

void MainWindow::onQuestionAnswered(const QString& answer)
{
    m_statusLabel->setText(QString("用户选择答案: %1").arg(answer));
    qDebug() << "Question answered:" << answer;
}

void MainWindow::onTaskError(const QString& errorMessage)
{
    m_statusLabel->setText(QString("错误: %1").arg(errorMessage));
    qWarning() << "Task error:" << errorMessage;

    QMessageBox::warning(this, "任务错误", errorMessage);
}

void MainWindow::onQuestionChanged(int currentIndex, int totalQuestions)
{
    // 更新导航按钮状态
    updateNavigationButtons();

    // 更新页面指示器显示题目信息
    if (totalQuestions > 0) {
        m_pageIndicator->setText(QString("第 %1 题，共 %2 题")
                                .arg(currentIndex + 1)
                                .arg(totalQuestions));
    }

    m_statusLabel->setText(QString("切换到第 %1 题").arg(currentIndex + 1));
}

void MainWindow::updateNavigationButtons()
{
    if (m_totalPages == 1 && !m_taskPages.isEmpty()) {
        // 单页面模式：根据题目状态控制导航按钮
        TaskPage* currentPage = m_taskPages[m_currentPageIndex];
        if (currentPage) {
            m_previousButton->setEnabled(currentPage->hasPreviousQuestion());
            m_nextButton->setEnabled(currentPage->hasNextQuestion());

            // 更新进度条（基于题目进度）
            int currentQuestionIndex = currentPage->getCurrentQuestionIndex();
            int totalQuestions = currentPage->getTotalQuestions();
            if (totalQuestions > 0) {
                int progress = ((currentQuestionIndex + 1) * 100) / totalQuestions;
                m_progressBar->setValue(progress);
            }
        } else {
            m_previousButton->setEnabled(false);
            m_nextButton->setEnabled(false);
        }
    } else {
        // 多页面模式：根据页面状态控制导航按钮
        m_previousButton->setEnabled(m_currentPageIndex > 0);
        m_nextButton->setEnabled(m_currentPageIndex < m_totalPages - 1);

        // 更新进度条（基于页面进度）
        if (m_totalPages > 0) {
            int progress = ((m_currentPageIndex + 1) * 100) / m_totalPages;
            m_progressBar->setValue(progress);
        }
    }
}

void MainWindow::updatePageIndicator()
{
    if (m_totalPages == 1 && !m_taskPages.isEmpty()) {
        // 单页面模式：显示题目信息
        TaskPage* currentPage = m_taskPages[m_currentPageIndex];
        if (currentPage) {
            int currentQuestionIndex = currentPage->getCurrentQuestionIndex();
            int totalQuestions = currentPage->getTotalQuestions();
            if (totalQuestions > 0) {
                m_pageIndicator->setText(QString("第 %1 题，共 %2 题")
                                        .arg(currentQuestionIndex + 1)
                                        .arg(totalQuestions));
            } else {
                m_pageIndicator->setText("第 1 题，共 1 题");
            }
        } else {
            m_pageIndicator->setText("第 1 题，共 1 题");
        }
    } else if (m_totalPages > 0) {
        // 多页面模式：显示页面信息
        m_pageIndicator->setText(QString("第 %1 页，共 %2 页")
                                .arg(m_currentPageIndex + 1)
                                .arg(m_totalPages));
    } else {
        m_pageIndicator->setText("无任务");
    }
}

void MainWindow::updateStatusBar()
{
    QString currentTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    m_timeLabel->setText(currentTime);
}

void MainWindow::onAboutApplication()
{
    QMessageBox::about(this, "关于",
        "<h3>药盒识别与用药建议系统</h3>"
        "<p>版本: 1.0.0</p>"
        "<p>基于深度学习的智能药盒识别和用药指导系统</p>"
        "<p>支持多种常见药品的识别和用药建议</p>"
}

void MainWindow::onShowHelp()
{
    QMessageBox::information(this, "使用帮助",
        "<h3>使用说明</h3>"
        "<p><b>1. 拍摄或上传药盒照片</b><br>"
        "点击相机按钮拍照或上传按钮选择图片文件</p>"
        "<p><b>2. 输入症状描述</b><br>"
        "在症状输入框中详细描述您的症状</p>"
        "<p><b>3. 点击推理按钮</b><br>"
        "系统将分析图片和症状，给出用药建议</p>"
        "<p><b>4. 查看结果</b><br>"
        "系统会显示识别的药品和详细的用药说明</p>"
        "<p><b>快捷键:</b><br>"
        "← → 方向键: 切换页面<br>"
        "Ctrl+Q: 退出程序</p>");
}

void MainWindow::onShowSettings()
{
    // TODO: 实现设置对话框
    QMessageBox::information(this, "设置", "设置功能正在开发中...");
}

void MainWindow::keyPressEvent(QKeyEvent *event)
{
    switch (event->key()) {
    case Qt::Key_Left:
        if (m_currentPageIndex > 0) {
            onPreviousPage();
        }
        break;
    case Qt::Key_Right:
        if (m_currentPageIndex < m_totalPages - 1) {
            onNextPage();
        }
        break;
    default:
        QMainWindow::keyPressEvent(event);
        break;
    }
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    int ret = QMessageBox::question(this, "确认退出",
                                   "确定要退出药盒识别系统吗？",
                                   QMessageBox::Yes | QMessageBox::No,
                                   QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        saveWindowState();
        event->accept();
    } else {
        event->ignore();
    }
}

void MainWindow::saveWindowState()
{
    QSettings settings;
    settings.beginGroup("MainWindow");
    settings.setValue("geometry", saveGeometry());
    settings.setValue("windowState", saveState());
    settings.setValue("currentPage", m_currentPageIndex);
    settings.endGroup();
}

void MainWindow::restoreWindowState()
{
    QSettings settings;
    settings.beginGroup("MainWindow");
    restoreGeometry(settings.value("geometry").toByteArray());
    restoreState(settings.value("windowState").toByteArray());
    m_currentPageIndex = settings.value("currentPage", 0).toInt();
    settings.endGroup();
}
