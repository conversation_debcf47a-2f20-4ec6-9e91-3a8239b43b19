#!/bin/bash
# 药盒识别应用测试编译脚本
# 用于验证编译环境和依赖

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo_info "=== 药盒识别应用编译环境测试 ==="

# 检查基本工具
echo_info "检查基本编译工具..."

tools=("gcc" "g++" "make" "cmake")
for tool in "${tools[@]}"; do
    if command -v "$tool" &> /dev/null; then
        version=$($tool --version | head -1)
        echo_success "✓ $tool: $version"
    else
        echo_error "✗ $tool 未安装"
    fi
done

# 检查Qt环境
echo_info "检查Qt环境..."

if command -v qmake &> /dev/null; then
    qt_version=$(qmake --version | grep "Qt version" | cut -d' ' -f4)
    echo_success "✓ Qt版本: $qt_version"
    
    # 检查Qt模块
    qt_modules=("Core" "Gui" "Widgets" "Multimedia" "MultimediaWidgets")
    for module in "${qt_modules[@]}"; do
        if pkg-config --exists Qt5$module 2>/dev/null; then
            echo_success "✓ Qt5$module 可用"
        else
            echo_warning "⚠️ Qt5$module 可能不可用"
        fi
    done
else
    echo_error "✗ qmake 未找到"
    echo_info "安装建议: sudo apt install qtbase5-dev qtchooser qt5-qmake qtbase5-dev-tools"
fi

# 检查多媒体支持
echo_info "检查多媒体支持..."

if pkg-config --exists Qt5Multimedia 2>/dev/null; then
    echo_success "✓ Qt5Multimedia 可用"
else
    echo_warning "⚠️ Qt5Multimedia 不可用"
    echo_info "安装建议: sudo apt install qtmultimedia5-dev libqt5multimedia5-plugins"
fi

# 检查摄像头设备
echo_info "检查摄像头设备..."

if ls /dev/video* &> /dev/null; then
    video_devices=$(ls /dev/video* | wc -l)
    echo_success "✓ 找到 $video_devices 个视频设备"
    ls /dev/video*
else
    echo_warning "⚠️ 未找到摄像头设备"
fi

# 检查项目文件
echo_info "检查项目文件..."

project_files=(
    "DiagnosisApp.pro"
    "main.cpp"
    "mainwindow.h"
    "mainwindow.cpp"
    "taskpage.h"
    "taskpage.cpp"
    "taskmanager.h"
    "taskmanager.cpp"
    "modelinference.h"
    "modelinference.cpp"
    "camerawidget.h"
    "camerawidget.cpp"
    "taskdata.h"
)

missing_count=0
for file in "${project_files[@]}"; do
    if [[ -f "$file" ]]; then
        echo_success "✓ $file 存在"
    else
        echo_error "✗ $file 缺失"
        ((missing_count++))
    fi
done

if [[ $missing_count -eq 0 ]]; then
    echo_success "✅ 所有必需的源文件都存在"
else
    echo_warning "⚠️ 缺少 $missing_count 个源文件"
fi

# 检查配置文件
echo_info "检查配置文件..."

if [[ -f "config/default_tasks.json" ]]; then
    echo_success "✓ 默认任务配置存在"
    task_count=$(grep -o '"taskId"' config/default_tasks.json | wc -l)
    echo_info "配置了 $task_count 个任务"
else
    echo_warning "⚠️ 默认任务配置缺失"
fi

# 系统信息
echo_info "系统信息..."

echo_info "操作系统: $(uname -a)"
echo_info "内存: $(free -h | grep '^Mem:' | awk '{print $2}')"
echo_info "磁盘空间: $(df -h . | tail -1 | awk '{print $4}') 可用"

# 测试编译（仅生成Makefile）
echo_info "测试编译配置..."

if command -v qmake &> /dev/null; then
    mkdir -p test_build
    cd test_build
    
    if qmake ../DiagnosisApp.pro &> /dev/null; then
        echo_success "✓ qmake 配置成功"
        
        # 检查生成的Makefile
        if [[ -f "Makefile" ]]; then
            echo_success "✓ Makefile 生成成功"
        else
            echo_error "✗ Makefile 生成失败"
        fi
    else
        echo_error "✗ qmake 配置失败"
    fi
    
    cd ..
    rm -rf test_build
else
    echo_warning "⚠️ 跳过编译测试（qmake不可用）"
fi

echo_info "=== 测试完成 ==="

# 给出建议
echo_info "=== 建议 ==="

if command -v qmake &> /dev/null; then
    echo_success "✅ 环境基本就绪，可以尝试编译"
    echo_info "运行编译命令: ./build.sh"
else
    echo_warning "⚠️ 需要安装Qt开发环境"
    echo_info "Ubuntu/Debian安装命令:"
    echo_info "sudo apt update"
    echo_info "sudo apt install -y qtbase5-dev qtchooser qt5-qmake qtbase5-dev-tools"
    echo_info "sudo apt install -y qtmultimedia5-dev libqt5multimedia5-plugins"
fi

echo_info "测试脚本执行完成"
