#include "taskpage.h"
#include <QApplication>
#include <QMimeData>
#include <QImageReader>
#include <QStandardPaths>
#include <QDateTime>
#include <QMessageBox>
#include <QFileDialog>

// 常量定义已在头文件中声明，这里不需要重复定义

TaskPage::TaskPage(QWidget *parent)
    : QWidget(parent)
    , m_currentQuestionIndex(0)
    , m_mainLayout(nullptr)
    , m_cameraWidget(nullptr)
    , m_modelInference(nullptr)
    , m_hasImage(false)
    , m_isInferencing(false)
    , m_progressTimer(new QTimer(this))
    , m_choicesButtonGroup(new QButtonGroup(this))
    , m_progressValue(0)
{
    setupUI();
    connectSignals();

    // 设置拖拽支持
    setAcceptDrops(true);

    qDebug() << "TaskPage created";
}

TaskPage::~TaskPage()
{
    if (m_progressTimer) {
        m_progressTimer->stop();
    }
    qDebug() << "TaskPage destroyed";
}

void TaskPage::setupUI()
{
    // 主布局 - 水平布局
    m_mainLayout = new QHBoxLayout(this);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    m_mainLayout->setSpacing(15);

    // 设置各个区域
    setupOperationSection();
    setupQuestionSection();
    setupImageSection();
    setupInferenceSection();

    // 初始化组件
    m_cameraWidget = new CameraWidget(this);
    m_modelInference = new ModelInference(this);

    // 设置样式
    setStyleSheet(R"(
        QFrame {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        QPushButton {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #0056b3;
        }
        QPushButton:pressed {
            background-color: #004085;
        }
        QPushButton:disabled {
            background-color: #6c757d;
        }
        QLabel#titleLabel {
            font-size: 16px;
            font-weight: bold;
            color: #212529;
        }
        QTextEdit {
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 8px;
        }
    )");
}

void TaskPage::setupOperationSection()
{
    // 左侧操作指引区域 - 占据1/3空间
    m_operationFrame = new QFrame();
    m_operationFrame->setMinimumWidth(300);
    m_operationFrame->setMaximumWidth(350);
    m_operationLayout = new QVBoxLayout(m_operationFrame);
    m_operationLayout->setContentsMargins(20, 20, 20, 20);
    m_operationLayout->setSpacing(15);

    // 标题
    m_operationTitle = new QLabel("操作事项");
    m_operationTitle->setObjectName("titleLabel");
    m_operationTitle->setAlignment(Qt::AlignCenter);
    m_operationTitle->setStyleSheet("font-size: 20px; font-weight: bold; color: #0078d4; margin-bottom: 10px;");
    m_operationLayout->addWidget(m_operationTitle);

    // 操作步骤 - 增大字体和间距
    m_step1Label = new QLabel("步骤1: 根据题目要求拍摄图像头下摆放指定药盒");
    m_step1Label->setWordWrap(true);
    m_step1Label->setStyleSheet("color: #000000; font-size: 16px; padding: 8px; background-color: #f8f9fa; border-radius: 6px; margin: 4px;");
    m_operationLayout->addWidget(m_step1Label);

    m_step2Label = new QLabel("步骤2: 确认摆放正确后点击拍照按钮进行拍照");
    m_step2Label->setWordWrap(true);
    m_step2Label->setStyleSheet("color: #000000; font-size: 16px; padding: 8px; background-color: #f8f9fa; border-radius: 6px; margin: 4px;");
    m_operationLayout->addWidget(m_step2Label);

    m_step3Label = new QLabel("步骤3: 确认图片无误后点击模型推理进行模型调用");
    m_step3Label->setWordWrap(true);
    m_step3Label->setStyleSheet("color: #000000; font-size: 16px; padding: 8px; background-color: #f8f9fa; border-radius: 6px; margin: 4px;");
    m_operationLayout->addWidget(m_step3Label);

    m_step4Label = new QLabel("步骤4: 确认模型输出后对界面进行截屏，请确保截图中含题目、图像及模型输出内容");
    m_step4Label->setWordWrap(true);
    m_step4Label->setStyleSheet("color: #000000; font-size: 16px; padding: 8px; background-color: #f8f9fa; border-radius: 6px; margin: 4px;");
    m_operationLayout->addWidget(m_step4Label);

    m_step5Label = new QLabel("步骤5: 确认每道题目都回答完毕后退出应用");
    m_step5Label->setWordWrap(true);
    m_step5Label->setStyleSheet("color: #000000; font-size: 16px; padding: 8px; background-color: #f8f9fa; border-radius: 6px; margin: 4px;");
    m_operationLayout->addWidget(m_step5Label);

    m_operationLayout->addStretch();
    // 设置操作区域占据1/3的空间
    m_mainLayout->addWidget(m_operationFrame, 1);
}

void TaskPage::setupQuestionSection()
{
    // 右侧主要区域
    m_rightFrame = new QFrame();
    m_rightLayout = new QVBoxLayout(m_rightFrame);
    m_rightLayout->setContentsMargins(15, 15, 15, 15);
    m_rightLayout->setSpacing(15);

    // 题目区域
    m_questionFrame = new QFrame();
    m_questionFrame->setMinimumHeight(200);
    m_questionLayout = new QVBoxLayout(m_questionFrame);
    m_questionLayout->setContentsMargins(15, 15, 15, 15);
    m_questionLayout->setSpacing(10);

    // 题目标题和清空按钮
    m_questionHeaderLayout = new QHBoxLayout();

    m_questionTitleLabel = new QLabel("药盒：健胃消食片");
    m_questionTitleLabel->setObjectName("titleLabel");
    m_questionHeaderLayout->addWidget(m_questionTitleLabel);

    m_questionHeaderLayout->addStretch();

    // 清空按钮
    m_clearButton = new QPushButton("清空");
    m_clearButton->setFixedSize(60, 30);
    m_clearButton->setStyleSheet(
        "QPushButton {"
        "    background-color: #dc3545;"
        "    color: white;"
        "    border: 1px solid #c82333;"
        "    border-radius: 4px;"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "}"
        "QPushButton:hover {"
        "    background-color: #c82333;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #bd2130;"
        "}"
    );
    m_clearButton->setToolTip("清空当前题目的所有信息");
    m_questionHeaderLayout->addWidget(m_clearButton);

    m_questionLayout->addLayout(m_questionHeaderLayout);

    // 题目内容
    m_questionTextLabel = new QLabel("题目1：图中是什么药品？只需返回正确的选项编号。");
    m_questionTextLabel->setWordWrap(true);
    m_questionTextLabel->setStyleSheet("color: #000000; font-size: 14px; font-weight: normal;");
    m_questionLayout->addWidget(m_questionTextLabel);

    // 选择题选项区域 - 增加间距避免重叠
    m_choicesFrame = new QFrame();
    m_choicesLayout = new QVBoxLayout(m_choicesFrame);
    m_choicesLayout->setContentsMargins(15, 15, 15, 15);
    m_choicesLayout->setSpacing(12);  // 增加选项间距
    m_questionLayout->addWidget(m_choicesFrame);

    m_questionLayout->addStretch();
    m_rightLayout->addWidget(m_questionFrame);

    m_mainLayout->addWidget(m_rightFrame, 1);
}

void TaskPage::setupImageSection()
{
    // 按钮区域
    m_buttonLayout = new QHBoxLayout();

    m_takePhotoButton = new QPushButton("拍照");
    m_takePhotoButton->setMinimumHeight(40);
    m_buttonLayout->addWidget(m_takePhotoButton);

    // 上传图片按钮 - 缩小尺寸
    m_uploadButton = new QPushButton("上传");
    m_uploadButton->setMaximumWidth(80);
    m_uploadButton->setMinimumHeight(30);
    m_uploadButton->setStyleSheet("font-size: 12px; padding: 4px 8px;");
    m_buttonLayout->addWidget(m_uploadButton);

    m_inferenceButton = new QPushButton("模型推理");
    m_inferenceButton->setMinimumHeight(40);
    m_buttonLayout->addWidget(m_inferenceButton);

    m_rightLayout->addLayout(m_buttonLayout);

    // 图片和推理结果区域
    m_contentLayout = new QHBoxLayout();

    // 图片预览区域
    m_imageFrame = new QFrame();
    m_imageFrame->setMinimumSize(300, 250);
    m_imageLayout = new QVBoxLayout(m_imageFrame);
    m_imageLayout->setContentsMargins(10, 10, 10, 10);

    m_imageAreaLabel = new QLabel("图片预览区");
    m_imageAreaLabel->setAlignment(Qt::AlignCenter);
    m_imageAreaLabel->setStyleSheet("font-weight: bold; color: #666;");
    m_imageLayout->addWidget(m_imageAreaLabel);

    m_imageLabel = new QLabel("点击拍照按钮获取图片");
    m_imageLabel->setAlignment(Qt::AlignCenter);
    m_imageLabel->setMinimumSize(280, 200);
    m_imageLabel->setStyleSheet("border: 2px dashed #ccc; border-radius: 8px; color: #888;");
    m_imageLayout->addWidget(m_imageLabel);

    m_contentLayout->addWidget(m_imageFrame);

    // 推理结果区域
    m_inferenceFrame = new QFrame();
    m_inferenceFrame->setMinimumSize(300, 250);
    m_inferenceLayout = new QVBoxLayout(m_inferenceFrame);
    m_inferenceLayout->setContentsMargins(10, 10, 10, 10);

    m_inferenceAreaLabel = new QLabel("模型输出区域");
    m_inferenceAreaLabel->setAlignment(Qt::AlignCenter);
    m_inferenceAreaLabel->setStyleSheet("font-weight: bold; color: #000000;");
    m_inferenceLayout->addWidget(m_inferenceAreaLabel);

    m_inferenceResultEdit = new QTextEdit();
    m_inferenceResultEdit->setStyleSheet("color: #000000; background-color: #ffffff; border: 1px solid #ccc;");
    m_inferenceResultEdit->setPlaceholderText("模型推理结果将在此显示");
    m_inferenceResultEdit->setMinimumHeight(200);
    m_inferenceLayout->addWidget(m_inferenceResultEdit);

    m_contentLayout->addWidget(m_inferenceFrame);

    m_rightLayout->addLayout(m_contentLayout);
}

void TaskPage::setupInferenceSection()
{
    // 删除模型测试按钮，只保留进度条
    // 进度条
    m_progressBar = new QProgressBar();
    m_progressBar->setVisible(false);
    m_progressBar->setMinimumHeight(25);
    m_rightLayout->addWidget(m_progressBar);

    m_progressLabel = new QLabel("准备就绪");
    m_progressLabel->setAlignment(Qt::AlignCenter);
    m_progressLabel->setStyleSheet("color: #666666;");
    m_rightLayout->addWidget(m_progressLabel);
}

// 加载题目数据
void TaskPage::loadQuestions(const QString& jsonFilePath)
{
    qDebug() << "Loading questions from:" << jsonFilePath;

    QFile file(jsonFilePath);
    if (!file.open(QIODevice::ReadOnly)) {
        QString errorMsg = QString("无法打开题目文件: %1").arg(jsonFilePath);
        qDebug() << errorMsg;
        QMessageBox::warning(this, "错误", errorMsg);
        return;
    }

    QByteArray data = file.readAll();
    QJsonDocument doc = QJsonDocument::fromJson(data);

    if (!doc.isArray()) {
        QString errorMsg = "题目文件格式错误";
        qDebug() << errorMsg;
        QMessageBox::warning(this, "错误", errorMsg);
        return;
    }

    QJsonArray questionsArray = doc.array();
    m_questions.clear();

    qDebug() << "Found" << questionsArray.size() << "questions in file";

    for (const auto& questionValue : questionsArray) {
        if (questionValue.isObject()) {
            QuestionData question = QuestionData::fromJson(questionValue.toObject());
            m_questions.append(question);
            qDebug() << "Loaded question:" << question.question.left(50) << "...";
        }
    }

    qDebug() << "Total questions loaded:" << m_questions.size();

    if (!m_questions.isEmpty()) {
        m_currentQuestionIndex = 0;
        displayCurrentQuestion();
        qDebug() << "Displaying first question";
    } else {
        qDebug() << "No questions to display, loading default questions";
        loadDefaultQuestions();
    }
}

// 加载默认题目（当文件加载失败时使用）
void TaskPage::loadDefaultQuestions()
{
    qDebug() << "Loading default questions";

    m_questions.clear();

    // 创建默认题目
    QuestionData q1;
    q1.question = "图中是什么药品？只需返回正确的选项编号。\nA. 西瓜霜润喉片\nB. 健胃消食片\nC. 消食健胃片\nD. 健冒消食片";
    q1.answer = "B";
    q1.questionType = "choice";
    q1.imagePaths << "default_image.jpg";
    m_questions.append(q1);

    QuestionData q2;
    q2.question = QString::fromUtf8("请回答以下判断题，你只需要输出错误或是正确\n题目如下：图中药品可以用于治疗急性胃肠炎。");
    q2.answer = QString::fromUtf8("错误");
    q2.questionType = "judgment";
    q2.imagePaths << "default_image.jpg";
    m_questions.append(q2);

    QuestionData q3;
    q3.question = "以下哪个是健胃消食片的主要功效？\nA. 清热解毒\nB. 健胃消食\nC. 止咳化痰\nD. 活血化瘀";
    q3.answer = "B";
    q3.questionType = "choice";
    q3.imagePaths << "default_image.jpg";
    m_questions.append(q3);

    qDebug() << "Loaded" << m_questions.size() << "default questions";

    if (!m_questions.isEmpty()) {
        m_currentQuestionIndex = 0;
        displayCurrentQuestion();
    }
}

void TaskPage::connectSignals()
{
    // 按钮信号
    connect(m_takePhotoButton, &QPushButton::clicked, this, &TaskPage::onTakePhoto);
    connect(m_uploadButton, &QPushButton::clicked, this, &TaskPage::onUploadImage);
    connect(m_inferenceButton, &QPushButton::clicked, this, &TaskPage::onStartInference);
    connect(m_clearButton, &QPushButton::clicked, this, &TaskPage::onClearCurrentQuestion);

    // 选择题答案信号
    connect(m_choicesButtonGroup, QOverload<QAbstractButton*>::of(&QButtonGroup::buttonClicked),
            this, &TaskPage::onAnswerSelected);

    // 模型推理信号
    connect(m_modelInference, &ModelInference::inferenceCompleted,
            this, &TaskPage::onInferenceCompleted);
    connect(m_modelInference, &ModelInference::inferenceError,
            this, &TaskPage::onInferenceError);

    // 相机信号
    connect(m_cameraWidget, &CameraWidget::imageCaptured,
            this, [this](const QString& imagePath) {
                loadImage(imagePath);
            });
}

void TaskPage::onTakePhoto()
{
    if (m_cameraWidget->isCameraAvailable()) {
        m_cameraWidget->startCamera();
        m_cameraWidget->captureImage();
    } else {
        emit taskError("摄像头不可用，请检查设备连接");
    }
}

void TaskPage::onUploadImage()
{
    // 创建文件对话框并设置样式
    QFileDialog dialog(this);
    dialog.setWindowTitle("选择药盒图片");
    dialog.setDirectory(QStandardPaths::writableLocation(QStandardPaths::PicturesLocation));
    dialog.setNameFilter("图片文件 (*.png *.jpg *.jpeg *.bmp *.gif)");
    dialog.setFileMode(QFileDialog::ExistingFile);
    dialog.setAcceptMode(QFileDialog::AcceptOpen);

    // 设置文件对话框样式，确保文字可见
    dialog.setStyleSheet(
        "QFileDialog {"
        "    color: #000000;"
        "    background-color: #ffffff;"
        "}"
        "QListView, QTreeView {"
        "    color: #000000;"
        "    background-color: #ffffff;"
        "    selection-background-color: #0078d4;"
        "    selection-color: #ffffff;"
        "}"
        "QLabel {"
        "    color: #000000;"
        "}"
        "QPushButton {"
        "    color: #000000;"
        "    background-color: #f0f0f0;"
        "    border: 1px solid #ccc;"
        "    padding: 4px 8px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #e0e0e0;"
        "}"
        "QLineEdit {"
        "    color: #000000;"
        "    background-color: #ffffff;"
        "    border: 1px solid #ccc;"
        "}"
    );

    QString fileName;
    if (dialog.exec() == QDialog::Accepted) {
        QStringList selectedFiles = dialog.selectedFiles();
        if (!selectedFiles.isEmpty()) {
            fileName = selectedFiles.first();
        }
    }

    if (!fileName.isEmpty()) {
        loadImage(fileName);
    }
}

void TaskPage::onStartInference()
{
    if (!m_hasImage) {
        QMessageBox::warning(this, "提示", "请先拍照获取图片");
        return;
    }

    callInferenceModel("question");
}



void TaskPage::onAnswerSelected()
{
    QRadioButton* selectedButton = qobject_cast<QRadioButton*>(m_choicesButtonGroup->checkedButton());
    if (selectedButton) {
        QString answer = selectedButton->text().left(1); // 获取A、B、C、D
        emit questionAnswered(answer);
        qDebug() << "Answer selected:" << answer;

        // 立即保存当前状态
        saveCurrentQuestionState();
    }
}

void TaskPage::onInferenceCompleted(const InferenceResult& result)
{
    m_isInferencing = false;
    showInferenceProgress(false);

    if (result.success) {
        // 显示推理结果
        m_inferenceResultEdit->setPlainText(result.getFormattedInstructions());
    } else {
        m_inferenceResultEdit->setPlainText("推理失败: " + result.errorMessage);
    }

    // 立即保存当前状态
    saveCurrentQuestionState();

    emit inferenceFinished(result);
}

// 显示当前题目
void TaskPage::displayCurrentQuestion()
{
    qDebug() << "displayCurrentQuestion called, index:" << m_currentQuestionIndex << "total:" << m_questions.size();

    if (m_currentQuestionIndex >= 0 && m_currentQuestionIndex < m_questions.size()) {
        const QuestionData& question = m_questions[m_currentQuestionIndex];

        qDebug() << "Displaying question:" << question.question.left(50) << "...";
        qDebug() << "Question type:" << question.questionType;

        // 更新题目标题
        m_questionTitleLabel->setText(QString("药盒：健胃消食片"));

        // 更新题目内容
        QString questionText = question.getQuestionText();
        m_questionTextLabel->setText(questionText);
        qDebug() << "Question text set to:" << questionText.left(50) << "...";

        // 清空之前的选项
        QLayoutItem* item;
        while ((item = m_choicesLayout->takeAt(0)) != nullptr) {
            delete item->widget();
            delete item;
        }

        // 添加新的选项
        if (question.questionType == "choice") {
            QStringList choices = question.getChoices();
            qDebug() << "Adding" << choices.size() << "choices";
            for (const QString& choice : choices) {
                QRadioButton* radioButton = new QRadioButton(choice);
                radioButton->setStyleSheet(
                    "QRadioButton {"
                    "    color: white;"
                    "    background-color: #0078d4;"
                    "    border: 1px solid #005a9e;"
                    "    border-radius: 6px;"
                    "    padding: 8px 16px;"
                    "    font-size: 14px;"
                    "    font-weight: bold;"
                    "    margin: 4px;"
                    "    min-height: 12px;"
                    "}"
                    "QRadioButton::indicator {"
                    "    width: 20px;"
                    "    height: 20px;"
                    "    border-radius: 10px;"
                    "    border: 2px solid white;"
                    "    background-color: transparent;"
                    "    margin-right: 8px;"
                    "}"
                    "QRadioButton::indicator:checked {"
                    "    background-color: white;"
                    "    border: 2px solid white;"
                    "}"
                    "QRadioButton:hover {"
                    "    background-color: #106ebe;"
                    "}"
                );
                m_choicesButtonGroup->addButton(radioButton);
                m_choicesLayout->addWidget(radioButton);
                qDebug() << "Added choice:" << choice;
            }
        } else if (question.questionType == "judgment") {
            qDebug() << "Adding judgment options";
            QRadioButton* trueButton = new QRadioButton("正确");
            QRadioButton* falseButton = new QRadioButton("错误");

            QString radioButtonStyle =
                "QRadioButton {"
                "    color: white;"
                "    background-color: #0078d4;"
                "    border: 1px solid #005a9e;"
                "    border-radius: 6px;"
                "    padding: 12px 16px;"
                "    font-size: 14px;"
                "    font-weight: bold;"
                "    margin: 4px;"
                "    min-height: 20px;"
                "}"
                "QRadioButton::indicator {"
                "    width: 20px;"
                "    height: 20px;"
                "    border-radius: 10px;"
                "    border: 2px solid white;"
                "    background-color: transparent;"
                "    margin-right: 8px;"
                "}"
                "QRadioButton::indicator:checked {"
                "    background-color: white;"
                "    border: 2px solid white;"
                "}"
                "QRadioButton:hover {"
                "    background-color: #106ebe;"
                "}";

            trueButton->setStyleSheet(radioButtonStyle);
            falseButton->setStyleSheet(radioButtonStyle);

            m_choicesButtonGroup->addButton(trueButton);
            m_choicesButtonGroup->addButton(falseButton);
            m_choicesLayout->addWidget(trueButton);
            m_choicesLayout->addWidget(falseButton);
        }
    } else {
        qDebug() << "No valid question to display";
    }
}



void TaskPage::onInferenceError(const QString& errorMessage)
{
    m_isInferencing = false;
    showInferenceProgress(false);
    m_inferenceResultEdit->setPlainText("推理错误: " + errorMessage);
}

// 调用推理模型
void TaskPage::callInferenceModel(const QString& modelType)
{
    m_isInferencing = true;
    showInferenceProgress(true);

#ifdef RK3588_PLATFORM
    // 在RK3588平台上使用真实的模型推理
    m_progressLabel->setText("正在进行RK3588模型推理...");
    m_modelInference->startRK3588Inference(m_currentImagePath);
#else
    // 在其他平台上使用模拟推理
    QString modelPath;
    QString resultText;

    if (modelType == "host") {
        // 主机模型路径 - autobit_back/yolo-rk3588/models/
        modelPath = "../autobit_back/yolo-rk3588/models/yolov8n.onnx";
        resultText = "调用主机YOLO模型进行目标检测...";
        m_progressLabel->setText("正在使用主机模型推理...");
    } else if (modelType == "rk3588") {
        // RK3588模型路径 - autobit_back/yolo-rk3588/models/
        modelPath = "../autobit_back/yolo-rk3588/models/yolov8n.rknn";
        resultText = "调用RK3588 YOLO模型进行目标检测...";
        m_progressLabel->setText("正在使用RK3588模型推理...");
    } else {
        // 题目推理模型路径（默认）
        modelPath = "/path/to/question/model";
        resultText = "调用题目推理模型...";
        m_progressLabel->setText("正在进行题目推理...");
    }

    // 模拟推理过程
    QTimer::singleShot(2000, [this, modelType, modelPath]() {
        InferenceResult result;
        result.success = true;
        result.detectedMedicine = "健胃消食片";

        if (modelType == "host") {
            result.dosageInstructions = QString("主机模型推理结果:\n模型路径: %1\n检测到目标: 药盒\n置信度: 0.85\n边界框: [120, 80, 300, 200]").arg(modelPath);
            result.confidence = 0.85f;
        } else if (modelType == "rk3588") {
            result.dosageInstructions = QString("RK3588模型推理结果:\n模型路径: %1\n检测到目标: 药盒\n置信度: 0.82\n边界框: [115, 75, 305, 205]").arg(modelPath);
            result.confidence = 0.82f;
        } else {
            result.dosageInstructions = QString("题目推理结果:\n识别药品: 健胃消食片\n建议答案: B\n置信度: 0.92");
            result.confidence = 0.92f;
        }

        onInferenceCompleted(result);
    });
#endif
}

// 获取当前题目
QuestionData TaskPage::getCurrentQuestion() const
{
    if (m_currentQuestionIndex >= 0 && m_currentQuestionIndex < m_questions.size()) {
        return m_questions[m_currentQuestionIndex];
    }
    return QuestionData();
}

// 题目导航方法
void TaskPage::goToNextQuestion()
{
    if (hasNextQuestion()) {
        // 保存当前题目状态
        saveCurrentQuestionState();

        m_currentQuestionIndex++;
        displayCurrentQuestion();

        // 恢复新题目的状态
        restoreQuestionState(m_currentQuestionIndex);

        emit questionChanged(m_currentQuestionIndex, m_questions.size());
        qDebug() << "Moved to next question:" << m_currentQuestionIndex;
    }
}

void TaskPage::goToPreviousQuestion()
{
    if (hasPreviousQuestion()) {
        // 保存当前题目状态
        saveCurrentQuestionState();

        m_currentQuestionIndex--;
        displayCurrentQuestion();

        // 恢复新题目的状态
        restoreQuestionState(m_currentQuestionIndex);

        emit questionChanged(m_currentQuestionIndex, m_questions.size());
        qDebug() << "Moved to previous question:" << m_currentQuestionIndex;
    }
}

bool TaskPage::hasNextQuestion() const
{
    return m_currentQuestionIndex < m_questions.size() - 1;
}

bool TaskPage::hasPreviousQuestion() const
{
    return m_currentQuestionIndex > 0;
}

void TaskPage::updateInferenceProgress()
{
    if (m_isInferencing && m_progressTimer) {
        m_progressValue = (m_progressValue + 5) % 100;
        m_progressBar->setValue(m_progressValue);
    }
}

void TaskPage::loadImage(const QString& imagePath)
{
    QImageReader reader(imagePath);
    if (!reader.canRead()) {
        emit taskError("无法读取图片文件: " + imagePath);
        return;
    }
    
    QImage image = reader.read();
    if (image.isNull()) {
        emit taskError("图片文件损坏或格式不支持");
        return;
    }
    
    // 缩放图片以适应显示区域
    QPixmap pixmap = QPixmap::fromImage(image);
    displayImage(pixmap);
    
    m_currentImagePath = imagePath;
    m_hasImage = true;
    
    QFileInfo fileInfo(imagePath);
    // 更新图片区域标签显示文件信息
    QString fileInfoText = QString("已选择: %1 (%2)")
                           .arg(fileInfo.fileName())
                           .arg(fileInfo.size() > 1024*1024 ?
                                QString("%1 MB").arg(fileInfo.size()/1024.0/1024.0, 0, 'f', 1) :
                                QString("%1 KB").arg(fileInfo.size()/1024.0, 0, 'f', 1));
    m_imageAreaLabel->setText(fileInfoText);
    
    emit imageUploaded(imagePath);

    // 立即保存当前状态
    saveCurrentQuestionState();

    qDebug() << "Image loaded:" << imagePath;
}

void TaskPage::displayImage(const QPixmap& pixmap)
{
    QPixmap scaledPixmap = pixmap.scaled(
        IMAGE_DISPLAY_WIDTH, IMAGE_DISPLAY_HEIGHT,
        Qt::KeepAspectRatio, Qt::SmoothTransformation
    );
    
    m_imageLabel->setPixmap(scaledPixmap);
    m_imageLabel->setStyleSheet("border: 2px solid #0078d4; border-radius: 8px;");
}

void TaskPage::showInferenceProgress(bool show)
{
    m_progressBar->setVisible(show);
    m_progressLabel->setVisible(show);
    m_inferenceButton->setEnabled(!show);
    
    if (show) {
        m_progressValue = 0;
        m_progressBar->setValue(0);
        m_progressLabel->setText("正在初始化推理...");
        
        // 启动进度更新定时器
        if (!m_progressTimer) {
            m_progressTimer = new QTimer(this);
            connect(m_progressTimer, &QTimer::timeout, this, &TaskPage::updateInferenceProgress);
        }
        m_progressTimer->start(200); // 每200ms更新一次
    } else {
        if (m_progressTimer) {
            m_progressTimer->stop();
        }
        m_progressBar->setValue(100);
        m_progressLabel->setText("推理完成");
    }
}

void TaskPage::resetPage()
{
    // 清空图片
    m_currentImagePath.clear();
    m_hasImage = false;
    m_imageLabel->clear();
    m_imageLabel->setText("点击拍照按钮获取图片");
    m_imageLabel->setStyleSheet("border: 2px dashed #ccc; border-radius: 8px; color: #888;");

    // 清空推理结果
    m_inferenceResultEdit->clear();

    // 重置题目到第一题
    if (!m_questions.isEmpty()) {
        m_currentQuestionIndex = 0;
        displayCurrentQuestion();
    }
}

void TaskPage::setPageEnabled(bool enabled)
{
    setEnabled(enabled);
}

void TaskPage::dragEnterEvent(QDragEnterEvent *event)
{
    if (event->mimeData()->hasUrls()) {
        QList<QUrl> urls = event->mimeData()->urls();
        if (!urls.isEmpty()) {
            QString fileName = urls.first().toLocalFile();
            QStringList supportedFormats = {"png", "jpg", "jpeg", "bmp", "gif"};
            QString suffix = QFileInfo(fileName).suffix().toLower();
            
            if (supportedFormats.contains(suffix)) {
                event->acceptProposedAction();
                return;
            }
        }
    }
    event->ignore();
}

void TaskPage::dropEvent(QDropEvent *event)
{
    QList<QUrl> urls = event->mimeData()->urls();
    if (!urls.isEmpty()) {
        QString fileName = urls.first().toLocalFile();
        loadImage(fileName);
        event->acceptProposedAction();
    }
}

// 状态保存和恢复方法
void TaskPage::saveCurrentQuestionState()
{
    if (m_currentQuestionIndex >= 0 && m_currentQuestionIndex < m_questions.size()) {
        QuestionState& state = m_questionStates[m_currentQuestionIndex];

        // 保存图片信息
        state.imagePath = m_currentImagePath;
        state.hasImage = m_hasImage;
        if (m_hasImage && m_imageLabel->pixmap() && !m_imageLabel->pixmap()->isNull()) {
            state.imagePixmap = *m_imageLabel->pixmap();
        }

        // 保存推理结果
        state.inferenceResult = m_inferenceResultEdit->toPlainText();

        // 保存选择的答案
        QAbstractButton* checkedButton = m_choicesButtonGroup->checkedButton();
        if (checkedButton) {
            state.selectedAnswer = checkedButton->text();
        } else {
            state.selectedAnswer.clear();
        }

        qDebug() << "Saved state for question" << m_currentQuestionIndex
                 << "- hasImage:" << state.hasImage
                 << "- answer:" << state.selectedAnswer;
    }
}

void TaskPage::restoreQuestionState(int questionIndex)
{
    if (m_questionStates.contains(questionIndex)) {
        const QuestionState& state = m_questionStates[questionIndex];

        // 恢复图片
        if (state.hasImage && !state.imagePixmap.isNull()) {
            m_currentImagePath = state.imagePath;
            m_hasImage = state.hasImage;
            m_imageLabel->setPixmap(state.imagePixmap);
            m_imageLabel->setStyleSheet("border: 2px solid #0078d4; border-radius: 8px;");

            // 更新图片区域标签
            QFileInfo fileInfo(state.imagePath);
            QString fileInfoText = QString("已选择: %1").arg(fileInfo.fileName());
            m_imageAreaLabel->setText(fileInfoText);
        } else {
            // 清空图片显示
            m_currentImagePath.clear();
            m_hasImage = false;
            m_imageLabel->clear();
            m_imageLabel->setText("点击拍照按钮获取图片");
            m_imageLabel->setStyleSheet("border: 2px dashed #ccc; border-radius: 8px; color: #888;");
            m_imageAreaLabel->setText("图片预览区");
        }

        // 恢复推理结果
        m_inferenceResultEdit->setPlainText(state.inferenceResult);

        // 恢复选择的答案
        if (!state.selectedAnswer.isEmpty()) {
            QList<QAbstractButton*> buttons = m_choicesButtonGroup->buttons();
            for (QAbstractButton* button : buttons) {
                if (button->text() == state.selectedAnswer) {
                    button->setChecked(true);
                    break;
                }
            }
        } else {
            // 清空所有选择
            QAbstractButton* checkedButton = m_choicesButtonGroup->checkedButton();
            if (checkedButton) {
                checkedButton->setChecked(false);
            }
        }

        qDebug() << "Restored state for question" << questionIndex
                 << "- hasImage:" << state.hasImage
                 << "- answer:" << state.selectedAnswer;
    } else {
        // 没有保存的状态，清空所有内容
        clearCurrentQuestionState();
    }
}

void TaskPage::clearCurrentQuestionState()
{
    // 清空图片
    m_currentImagePath.clear();
    m_hasImage = false;
    m_imageLabel->clear();
    m_imageLabel->setText("点击拍照按钮获取图片");
    m_imageLabel->setStyleSheet("border: 2px dashed #ccc; border-radius: 8px; color: #888;");
    m_imageAreaLabel->setText("图片预览区");

    // 清空推理结果
    m_inferenceResultEdit->clear();

    // 清空选择的答案
    QAbstractButton* checkedButton = m_choicesButtonGroup->checkedButton();
    if (checkedButton) {
        checkedButton->setChecked(false);
    }

    qDebug() << "Cleared state for current question";
}

void TaskPage::onClearCurrentQuestion()
{
    int ret = QMessageBox::question(this, "确认清空",
                                   "确定要清空当前题目的所有信息吗？\n（包括图片、推理结果和选择的答案）",
                                   QMessageBox::Yes | QMessageBox::No,
                                   QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        // 清空当前显示的内容
        clearCurrentQuestionState();

        // 删除保存的状态
        if (m_questionStates.contains(m_currentQuestionIndex)) {
            m_questionStates.remove(m_currentQuestionIndex);
        }

        qDebug() << "User cleared question" << m_currentQuestionIndex;
    }
}
