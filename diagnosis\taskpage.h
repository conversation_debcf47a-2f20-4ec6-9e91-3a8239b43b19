#ifndef TASKPAGE_H
#define TASKPAGE_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QTextEdit>
#include <QLineEdit>
#include <QFrame>
#include <QScrollArea>
#include <QProgressBar>
#include <QTimer>
#include <QFileDialog>
#include <QPixmap>
#include <QMovie>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QMimeData>
#include <QUrl>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>
#include <QFile>
#include <QRadioButton>
#include <QButtonGroup>
#include <QGroupBox>

#include "taskdata.h"
#include "camerawidget.h"
#include "modelinference.h"

/**
 * @brief 任务页面类
 * 每个任务页面包含完整的药盒识别和用药建议功能
 */
class TaskPage : public QWidget
{
    Q_OBJECT

public:
    explicit TaskPage(QWidget *parent = nullptr);
    ~TaskPage();

    // 加载题目数据
    void loadQuestions(const QString& jsonFilePath);
    void loadDefaultQuestions();

    // 重置页面状态
    void resetPage();

    // 设置页面启用状态
    void setPageEnabled(bool enabled);

    // 获取题目状态
    int getCurrentQuestionIndex() const { return m_currentQuestionIndex; }
    int getTotalQuestions() const { return m_questions.size(); }

    // 获取当前题目
    QuestionData getCurrentQuestion() const;

    // 题目导航方法
    void goToNextQuestion();
    void goToPreviousQuestion();
    bool hasNextQuestion() const;
    bool hasPreviousQuestion() const;

public slots:

signals:
    void questionAnswered(const QString& answer);
    void taskError(const QString& errorMessage);
    void imageUploaded(const QString& imagePath);
    void inferenceStarted();
    void inferenceFinished(const InferenceResult& result);
    void questionChanged(int currentIndex, int totalQuestions);

protected:
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dropEvent(QDropEvent *event) override;

private slots:
    void onTakePhoto();
    void onUploadImage();
    void onStartInference();
    void onInferenceCompleted(const InferenceResult& result);
    void onInferenceError(const QString& errorMessage);
    void onAnswerSelected();
    void updateInferenceProgress();

private:
    void setupUI();
    void setupOperationSection();
    void setupQuestionSection();
    void setupImageSection();
    void setupInferenceSection();
    void connectSignals();

    void loadImage(const QString& imagePath);
    void displayImage(const QPixmap& pixmap);
    void showInferenceProgress(bool show);
    void displayCurrentQuestion();
    void callInferenceModel(const QString& modelType);
    
    // 题目数据
    QList<QuestionData> m_questions;
    int m_currentQuestionIndex;

    // UI组件 - 主布局
    QHBoxLayout *m_mainLayout;

    // UI组件 - 左侧操作区域
    QFrame *m_operationFrame;
    QVBoxLayout *m_operationLayout;
    QLabel *m_operationTitle;
    QLabel *m_step1Label;
    QLabel *m_step2Label;
    QLabel *m_step3Label;
    QLabel *m_step4Label;
    QLabel *m_step5Label;

    // UI组件 - 右侧主要区域
    QFrame *m_rightFrame;
    QVBoxLayout *m_rightLayout;

    // UI组件 - 题目区域
    QFrame *m_questionFrame;
    QVBoxLayout *m_questionLayout;
    QLabel *m_questionTitleLabel;
    QLabel *m_questionTextLabel;
    QFrame *m_choicesFrame;
    QVBoxLayout *m_choicesLayout;
    QButtonGroup *m_choicesButtonGroup;
    
    // UI组件 - 按钮区域
    QHBoxLayout *m_buttonLayout;
    QPushButton *m_takePhotoButton;
    QPushButton *m_uploadButton;
    QPushButton *m_inferenceButton;

    // UI组件 - 图片和推理区域
    QHBoxLayout *m_contentLayout;
    QFrame *m_imageFrame;
    QVBoxLayout *m_imageLayout;
    QLabel *m_imageAreaLabel;
    QLabel *m_imageLabel;

    QFrame *m_inferenceFrame;
    QVBoxLayout *m_inferenceLayout;
    QLabel *m_inferenceAreaLabel;
    QTextEdit *m_inferenceResultEdit;



    // UI组件 - 进度条
    QProgressBar *m_progressBar;
    QLabel *m_progressLabel;
    
    // 核心组件
    CameraWidget *m_cameraWidget;
    ModelInference *m_modelInference;

    // 状态变量
    QString m_currentImagePath;
    bool m_hasImage;
    bool m_isInferencing;
    QTimer *m_progressTimer;
    int m_progressValue;

    // 常量
    static const int IMAGE_DISPLAY_WIDTH = 400;
    static const int IMAGE_DISPLAY_HEIGHT = 300;
    static const int RESULT_IMAGE_WIDTH = 200;
    static const int RESULT_IMAGE_HEIGHT = 150;
    static const int FRAME_MARGIN = 15;
    static const int FRAME_SPACING = 10;
};

#endif // TASKPAGE_H
