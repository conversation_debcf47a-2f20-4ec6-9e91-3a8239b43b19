# DiagnosisApp RK3588部署指南

## 系统要求

- **硬件**: RK3588开发板
- **系统**: Debian
- **Qt版本**: 5.15.2
- **架构**: ARM64 (aarch64)

## 模型文件要求

确保以下模型文件存在于RK3588设备上：

```bash
/userdata/demo                                    # 推理可执行程序
/userdata/models/qwen2_vl_2b_vision_rk3588.rknn  # RKNN视觉模型
/userdata/models/Qwen2-2B-vl-Instruct.rkllm     # RKLLM语言模型
```

## 编译和部署

### 1. 环境准备

```bash
# 安装Qt5开发包
sudo apt update
sudo apt install qtbase5-dev qtchooser qt5-qmake qtbase5-dev-tools
sudo apt install qtmultimedia5-dev

# 检查Qt版本
qmake --version
```

### 2. 编译应用

```bash
# 进入项目目录
cd test

# 编译（会自动检测ARM64架构并启用RK3588配置）
./build.sh

# 或者手动指定RK3588平台
./build.sh --platform rk3588
```

### 3. 运行应用

```bash
cd build
./DiagnosisApp
```

## 模型推理流程

### 推理命令格式

```bash
./demo <image_path> <rknn_model> <rkllm_model> <max_tokens> <context_length>
```

### 实际调用示例

```bash
./demo /tmp/medicine.jpg \
       /userdata/models/qwen2_vl_2b_vision_rk3588.rknn \
       /userdata/models/Qwen2-2B-vl-Instruct.rkllm \
       128 512
```

### 交互流程

1. 程序启动后会提示选择语言：
   ```
   Please select language:
   0: English
   1: Chinese
   ```

2. 应用会自动输入 `1` 选择中文输出

3. 模型返回中文描述，例如：
   ```
   这是一盒健胃消食片，主要用于治疗消化不良、食欲不振等症状。
   建议饭后服用，成人一次4-6片，一日3次。
   ```

## 代码适配说明

### 平台检测

代码通过预编译宏 `RK3588_PLATFORM` 来区分平台：

```cpp
#ifdef RK3588_PLATFORM
    // RK3588平台：使用真实模型推理
    m_modelInference->startRK3588Inference(m_currentImagePath);
#else
    // 其他平台：使用模拟推理
    // ... 模拟代码
#endif
```

### 模型推理适配

- **自动输入**: 应用会自动向推理程序输入 `1` 选择中文
- **输出解析**: 解析中文输出并提取药品信息
- **错误处理**: 处理模型文件不存在、程序崩溃等情况
- **超时机制**: 30秒推理超时保护

## 故障排除

### 1. 编译错误

**问题**: `unknown modules in Qt: core gui Core Gui Widgets`

**解决**: 已修复，使用大写模块名避免重复定义

### 2. 模型文件不存在

**问题**: 推理失败，提示模型文件不存在

**解决**: 
```bash
# 检查模型文件
ls -la /userdata/models/
ls -la /userdata/demo

# 确保文件权限正确
chmod +x /userdata/demo
```

### 3. 推理程序无响应

**问题**: 推理程序启动但无输出

**解决**: 
- 检查推理程序是否需要额外的依赖库
- 确认输入图片格式正确
- 查看系统日志: `dmesg | tail`

### 4. Qt显示问题

**问题**: 界面无法显示

**解决**:
```bash
# 设置显示环境
export DISPLAY=:0

# 或使用X11转发
export DISPLAY=<host_ip>:0.0
```

## 性能优化建议

1. **内存管理**: RK3588内存有限，及时释放不用的图片资源
2. **推理优化**: 可以预加载模型以减少首次推理时间
3. **界面优化**: 在推理过程中显示进度条，提升用户体验

## 开发调试

### 模拟模式

在开发环境中，如果没有真实的RK3588模型，会自动使用模拟结果：

```cpp
if (!QFile::exists(demoPath)) {
    qWarning() << "RK3588 demo executable not found, using mock result";
    // 返回模拟结果
}
```

### 日志输出

启用详细日志：

```bash
export QT_LOGGING_RULES="*.debug=true"
./DiagnosisApp
```
