#ifndef TASKDATA_H
#define TASKDATA_H

#include <QString>
#include <QStringList>
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonArray>

/**
 * @brief 题目数据结构
 * 支持选择题和判断题
 */
struct QuestionData {
    QString question;              // 题目内容
    QString answer;                // 正确答案
    QStringList imagePaths;        // 图片路径列表
    QString questionType;          // 题目类型：choice/judgment

    // 默认构造函数
    QuestionData() {}

    // 从JSON对象创建QuestionData
    static QuestionData fromJson(const QJsonObject& json) {
        QuestionData data;
        data.question = json["Q"].toString();
        data.answer = json["A"].toString();

        // 解析图片路径
        QJsonArray imageArray = json["image"].toArray();
        for (const auto& imagePath : imageArray) {
            data.imagePaths.append(imagePath.toString());
        }

        // 判断题目类型
        if (data.question.contains("判断题") ||
            data.answer == "正确" || data.answer == "错误") {
            data.questionType = "judgment";
        } else {
            data.questionType = "choice";
        }

        return data;
    }

    // 获取选项列表（仅适用于选择题）
    QStringList getChoices() const {
        QStringList choices;
        if (questionType == "choice") {
            QStringList lines = question.split('\n');
            for (const QString& line : lines) {
                if (line.startsWith("A.") || line.startsWith("B.") ||
                    line.startsWith("C.") || line.startsWith("D.")) {
                    choices.append(line);
                }
            }
        }
        return choices;
    }

    // 获取题目主体（去除选项）
    QString getQuestionText() const {
        if (questionType == "choice") {
            QStringList lines = question.split('\n');
            QString questionText;
            for (const QString& line : lines) {
                if (!line.startsWith("A.") && !line.startsWith("B.") &&
                    !line.startsWith("C.") && !line.startsWith("D.")) {
                    if (!questionText.isEmpty()) questionText += "\n";
                    questionText += line;
                }
            }
            return questionText;
        }
        return question;
    }
};

/**
 * @brief 任务数据结构
 * 定义每个任务页面的配置信息
 */
struct TaskData {
    int taskId;                    // 任务ID
    QString taskName;              // 任务名称
    QString guidanceText;          // 指引说明文本
    QString symptomPlaceholder;    // 症状输入框占位符
    QString medicineType;          // 药盒类型
    QStringList expectedSymptoms;  // 预期症状列表
    QString modelPath;             // 模型文件路径
    QString configPath;            // 配置文件路径
    bool isActive;                 // 任务是否激活

    // 默认构造函数
    TaskData() : taskId(0), isActive(true) {}

    // 带参数构造函数
    TaskData(int id, const QString& name, const QString& guidance, 
             const QString& placeholder, const QString& medicine)
        : taskId(id), taskName(name), guidanceText(guidance), 
          symptomPlaceholder(placeholder), medicineType(medicine), isActive(true) {}

    // 从JSON对象创建TaskData
    static TaskData fromJson(const QJsonObject& json) {
        TaskData data;
        data.taskId = json["taskId"].toInt();
        data.taskName = json["taskName"].toString();
        data.guidanceText = json["guidanceText"].toString();
        data.symptomPlaceholder = json["symptomPlaceholder"].toString();
        data.medicineType = json["medicineType"].toString();
        data.modelPath = json["modelPath"].toString();
        data.configPath = json["configPath"].toString();
        data.isActive = json["isActive"].toBool(true);

        // 解析症状列表
        QJsonArray symptomsArray = json["expectedSymptoms"].toArray();
        for (const auto& symptom : symptomsArray) {
            data.expectedSymptoms.append(symptom.toString());
        }

        return data;
    }

    // 转换为JSON对象
    QJsonObject toJson() const {
        QJsonObject json;
        json["taskId"] = taskId;
        json["taskName"] = taskName;
        json["guidanceText"] = guidanceText;
        json["symptomPlaceholder"] = symptomPlaceholder;
        json["medicineType"] = medicineType;
        json["modelPath"] = modelPath;
        json["configPath"] = configPath;
        json["isActive"] = isActive;

        // 转换症状列表
        QJsonArray symptomsArray;
        for (const QString& symptom : expectedSymptoms) {
            symptomsArray.append(symptom);
        }
        json["expectedSymptoms"] = symptomsArray;

        return json;
    }

    // 验证数据完整性
    bool isValid() const {
        return taskId > 0 && 
               !taskName.isEmpty() && 
               !guidanceText.isEmpty() && 
               !medicineType.isEmpty();
    }
};

/**
 * @brief 推理结果数据结构
 */
struct InferenceResult {
    bool success;                  // 推理是否成功
    QString errorMessage;          // 错误信息
    QString detectedMedicine;      // 识别的药品名称
    QString medicineImage;         // 药盒图片路径（截取后的）
    QString dosageInstructions;    // 用药说明
    QString frequency;             // 服用频率
    QString timing;                // 服用时间
    QString contraindications;     // 禁忌事项
    QString sideEffects;           // 副作用
    float confidence;              // 置信度
    QStringList warnings;          // 警告信息

    // 默认构造函数
    InferenceResult() : success(false), confidence(0.0f) {}

    // 从JSON对象创建InferenceResult
    static InferenceResult fromJson(const QJsonObject& json) {
        InferenceResult result;
        result.success = json["success"].toBool();
        result.errorMessage = json["errorMessage"].toString();
        result.detectedMedicine = json["detectedMedicine"].toString();
        result.medicineImage = json["medicineImage"].toString();
        result.dosageInstructions = json["dosageInstructions"].toString();
        result.frequency = json["frequency"].toString();
        result.timing = json["timing"].toString();
        result.contraindications = json["contraindications"].toString();
        result.sideEffects = json["sideEffects"].toString();
        result.confidence = json["confidence"].toDouble();

        // 解析警告列表
        QJsonArray warningsArray = json["warnings"].toArray();
        for (const auto& warning : warningsArray) {
            result.warnings.append(warning.toString());
        }

        return result;
    }

    // 转换为JSON对象
    QJsonObject toJson() const {
        QJsonObject json;
        json["success"] = success;
        json["errorMessage"] = errorMessage;
        json["detectedMedicine"] = detectedMedicine;
        json["medicineImage"] = medicineImage;
        json["dosageInstructions"] = dosageInstructions;
        json["frequency"] = frequency;
        json["timing"] = timing;
        json["contraindications"] = contraindications;
        json["sideEffects"] = sideEffects;
        json["confidence"] = confidence;

        // 转换警告列表
        QJsonArray warningsArray;
        for (const QString& warning : warnings) {
            warningsArray.append(warning);
        }
        json["warnings"] = warningsArray;

        return json;
    }

    // 获取格式化的用药说明
    QString getFormattedInstructions() const {
        QString formatted;
        
        if (!detectedMedicine.isEmpty()) {
            formatted += QString("药品名称：%1\n\n").arg(detectedMedicine);
        }
        
        if (!dosageInstructions.isEmpty()) {
            formatted += QString("用药说明：%1\n\n").arg(dosageInstructions);
        }
        
        if (!frequency.isEmpty()) {
            formatted += QString("服用频率：%1\n\n").arg(frequency);
        }
        
        if (!timing.isEmpty()) {
            formatted += QString("服用时间：%1\n\n").arg(timing);
        }
        
        if (!contraindications.isEmpty()) {
            formatted += QString("禁忌事项：%1\n\n").arg(contraindications);
        }
        
        if (!sideEffects.isEmpty()) {
            formatted += QString("副作用：%1\n\n").arg(sideEffects);
        }
        
        if (!warnings.isEmpty()) {
            formatted += "注意事项：\n";
            for (const QString& warning : warnings) {
                formatted += QString("• %1\n").arg(warning);
            }
        }
        
        if (confidence > 0) {
            formatted += QString("\n识别置信度：%.1f%%").arg(confidence * 100);
        }
        
        return formatted;
    }
};

#endif // TASKDATA_H
