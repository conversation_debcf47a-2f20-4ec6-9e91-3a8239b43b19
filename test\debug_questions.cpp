#include <QCoreApplication>
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>
#include <QFile>
#include <QDebug>
#include "taskdata.h"

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    QString questionsPath = "config/questions.json";
    qDebug() << "Loading questions from:" << questionsPath;
    
    QFile file(questionsPath);
    if (!file.open(QIODevice::ReadOnly)) {
        qDebug() << "Cannot open file:" << questionsPath;
        return 1;
    }
    
    QByteArray data = file.readAll();
    qDebug() << "File content:" << data;
    
    QJsonDocument doc = QJsonDocument::fromJson(data);
    if (!doc.isArray()) {
        qDebug() << "Not a JSON array";
        return 1;
    }
    
    QJsonArray questionsArray = doc.array();
    qDebug() << "Found" << questionsArray.size() << "questions";
    
    QList<QuestionData> questions;
    for (const auto& questionValue : questionsArray) {
        if (questionValue.isObject()) {
            QJsonObject obj = questionValue.toObject();
            qDebug() << "Question object:" << obj;
            
            QuestionData question = QuestionData::fromJson(obj);
            questions.append(question);
            
            qDebug() << "Loaded question:";
            qDebug() << "  Question:" << question.question;
            qDebug() << "  Answer:" << question.answer;
            qDebug() << "  Type:" << question.questionType;
            qDebug() << "  Images:" << question.imagePaths;
        }
    }
    
    qDebug() << "Total questions loaded:" << questions.size();
    
    return 0;
}
