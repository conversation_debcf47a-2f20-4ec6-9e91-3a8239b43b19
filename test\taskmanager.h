#ifndef TASKMANAGER_H
#define TASKMANAGER_H

#include <QObject>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFile>
#include <QDir>
#include <QStandardPaths>
#include <QDebug>
#include "taskdata.h"

/**
 * @brief 任务管理器类
 * 负责加载、保存和管理任务配置数据
 */
class TaskManager : public QObject
{
    Q_OBJECT

public:
    explicit TaskManager(QObject *parent = nullptr);
    ~TaskManager();

    // 加载任务数据
    QList<TaskData> loadTasks();
    
    // 保存任务数据
    bool saveTasks(const QList<TaskData>& tasks);
    
    // 获取单个任务
    TaskData getTask(int taskId) const;
    
    // 更新任务
    bool updateTask(const TaskData& task);
    
    // 添加任务
    bool addTask(const TaskData& task);
    
    // 删除任务
    bool removeTask(int taskId);
    
    // 获取任务数量
    int getTaskCount() const;
    
    // 验证任务配置
    bool validateTaskConfig(const TaskData& task) const;
    
    // 创建默认任务配置
    void createDefaultTasks();
    
    // 重置所有任务
    void resetAllTasks();

signals:
    void tasksLoaded(int count);
    void taskUpdated(int taskId);
    void taskAdded(int taskId);
    void taskRemoved(int taskId);
    void configError(const QString& errorMessage);

private:
    void initializeConfigDirectory();
    QString getConfigFilePath() const;
    bool loadFromFile(const QString& filePath);
    bool saveToFile(const QString& filePath) const;
    TaskData createDefaultTask(int taskId, const QString& taskName, 
                              const QString& medicineType) const;
    
    // 任务数据
    QList<TaskData> m_tasks;
    QString m_configDir;
    QString m_configFile;
    
    // 常量
    static const QString CONFIG_DIR_NAME;
    static const QString CONFIG_FILE_NAME;
    static const int MAX_TASKS;
};

#endif // TASKMANAGER_H
