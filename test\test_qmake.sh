#!/bin/bash
# 简化的qmake测试脚本

echo "=== 测试qmake配置 ==="

# 查找qmake
QMAKE_PATH=""
QMAKE_CANDIDATES=(
    "/usr/bin/qmake"
    "/usr/bin/qmake-qt5"
    "/usr/lib/qt5/bin/qmake"
    "/usr/lib/aarch64-linux-gnu/qt5/bin/qmake"
    "qmake"
)

for candidate in "${QMAKE_CANDIDATES[@]}"; do
    if command -v "$candidate" >/dev/null 2>&1; then
        QMAKE_PATH="$candidate"
        echo "✓ 找到qmake: $QMAKE_PATH"
        break
    fi
done

if [[ -z "$QMAKE_PATH" ]]; then
    echo "✗ 未找到qmake"
    exit 1
fi

# 显示qmake信息
echo "qmake版本:"
$QMAKE_PATH --version

echo ""
echo "Qt配置信息:"
$QMAKE_PATH -query QT_INSTALL_PREFIX
$QMAKE_PATH -query QT_INSTALL_LIBS
$QMAKE_PATH -query QT_INSTALL_PLUGINS

echo ""
echo "=== 测试项目配置 ==="

# 创建临时测试目录
TEST_DIR="/tmp/qt_test_$$"
mkdir -p "$TEST_DIR"
cd "$TEST_DIR"

# 创建最小的测试项目
cat > test.pro << 'EOF'
QT += core gui widgets
TARGET = test
CONFIG += c++17

SOURCES += main.cpp
HEADERS += 

# 输出目录
DESTDIR = .
OBJECTS_DIR = obj
MOC_DIR = moc
EOF

cat > main.cpp << 'EOF'
#include <QApplication>
#include <QWidget>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    QWidget window;
    window.show();
    return app.exec();
}
EOF

echo "测试项目文件已创建在: $TEST_DIR"
echo ""

# 测试qmake
echo "运行qmake..."
if $QMAKE_PATH test.pro; then
    echo "✓ qmake成功"
    
    # 检查生成的Makefile
    if [[ -f "Makefile" ]]; then
        echo "✓ Makefile已生成"
        
        # 尝试编译
        echo ""
        echo "尝试编译..."
        if make -j$(nproc) 2>&1 | head -20; then
            echo "✓ 编译测试通过"
        else
            echo "✗ 编译失败，但qmake配置正常"
        fi
    else
        echo "✗ Makefile未生成"
    fi
else
    echo "✗ qmake失败"
    echo "错误信息:"
    $QMAKE_PATH test.pro 2>&1
fi

# 清理
cd /
rm -rf "$TEST_DIR"

echo ""
echo "=== 建议 ==="
echo "如果qmake失败，请尝试:"
echo "1. 安装完整的Qt开发包:"
echo "   sudo apt install qtbase5-dev qtmultimedia5-dev"
echo ""
echo "2. 设置Qt环境变量:"
echo "   export QT_SELECT=qt5"
echo ""
echo "3. 运行完整的环境检查:"
echo "   ./debug_qt_env.sh"
