# 药盒识别与用药建议系统

基于Qt5和深度学习的智能药盒识别和用药指导应用程序，适用于RK3588边缘设备。

## 🎯 功能特性

### 核心功能
- **多任务支持**: 支持感冒药、止痛药、消炎药、胃药、维生素等多种药品识别
- **智能识别**: 基于深度学习模型的药盒图像识别
- **用药建议**: 结合症状描述提供个性化用药指导
- **多页界面**: 每页代表一个独立任务，支持翻页切换
- **实时拍照**: 支持摄像头实时拍照和图片上传

### 界面特性
- **现代化UI**: 深色主题，美观易用
- **响应式设计**: 适配不同屏幕尺寸
- **拖拽支持**: 支持图片拖拽上传
- **进度显示**: 实时显示推理进度
- **快捷键**: 支持键盘快捷操作

### 技术特性
- **多线程**: 后台推理，不阻塞UI
- **配置化**: JSON配置文件，易于扩展
- **模块化**: 清晰的代码架构，便于维护
- **跨平台**: 基于Qt5，支持Linux/Windows

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层                                │
├─────────────────────┬───────────────────────────────────────┤
│   主窗口 (MainWindow) │        任务页面 (TaskPage)           │
│   - 导航控制         │        - 图片上传                     │
│   - 状态显示         │        - 症状输入                     │
│   - 菜单管理         │        - 推理控制                     │
├─────────────────────┴───────────────────────────────────────┤
│                   业务逻辑层                                │
│  任务管理器 (TaskManager) │ 模型推理 (ModelInference)        │
│  - 任务配置管理           │ - 推理线程管理                    │
│  - 数据持久化             │ - 结果解析                        │
├─────────────────────────────────────────────────────────────┤
│                   硬件接口层                                │
│  相机控件 (CameraWidget)  │ RKNN模型接口                     │
│  - 摄像头控制             │ - 模型加载                        │
│  - 图片捕获               │ - 推理执行                        │
└─────────────────────────────────────────────────────────────┘
```

## 📦 项目结构

```
diagnosis/
├── DiagnosisApp.pro          # Qt项目文件
├── DiagnosisApp.qrc          # Qt资源文件
├── main.cpp                  # 主程序入口
├── mainwindow.h/cpp          # 主窗口类
├── taskpage.h/cpp            # 任务页面类
├── taskmanager.h/cpp         # 任务管理器
├── modelinference.h/cpp      # 模型推理管理器
├── camerawidget.h/cpp        # 相机控件
├── taskdata.h                # 数据结构定义
├── build.sh                  # 编译脚本
├── README.md                 # 项目说明
├── config/                   # 配置文件目录
│   └── default_tasks.json    # 默认任务配置
├── icons/                    # 图标资源
├── images/                   # 图片资源
└── build/                    # 编译输出目录
```

## 🚀 快速开始

### 环境要求

- **操作系统**: Linux (Ubuntu 18.04+推荐)
- **Qt版本**: 5.12.6 (与autobit_back保持一致)
- **编译器**: GCC 7.0+
- **内存**: 建议4GB以上
- **存储**: 建议2GB以上可用空间

### 编译安装

1. **克隆项目**
```bash
cd /path/to/your/workspace
# 项目文件已在diagnosis目录中
```

2. **安装依赖**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y qtbase5-dev qtchooser qt5-qmake qtbase5-dev-tools
sudo apt install -y qtmultimedia5-dev libqt5multimedia5-plugins

# 或使用自定义Qt安装
export QT_PATH=/home/<USER>/qt512/5.12.6/gcc_64
```

3. **编译项目**
```bash
cd diagnosis
chmod +x build.sh
./build.sh
```

4. **运行应用**
```bash
# 方式1：直接运行
cd build
./DiagnosisApp

# 方式2：使用启动脚本
./build/run_diagnosis_app.sh
```

### 配置模型

1. **准备模型文件**
```bash
# 将RKNN模型文件放置到models目录
mkdir -p build/models
cp /path/to/your/models/*.rknn build/models/
```

2. **配置任务**
```bash
# 编辑任务配置文件
vim build/config/default_tasks.json
```

## 📖 使用说明

### 基本操作流程

1. **启动应用程序**
   - 运行编译后的可执行文件
   - 应用程序将显示第一个任务页面

2. **选择任务**
   - 使用导航按钮或方向键切换任务页面
   - 每个页面对应不同类型的药品识别

3. **上传图片**
   - 点击"拍照"按钮使用摄像头拍摄
   - 点击"上传"按钮选择本地图片文件
   - 支持拖拽图片到指定区域

4. **输入症状**
   - 在症状输入框中详细描述症状
   - 系统会根据症状和图片进行综合分析

5. **开始推理**
   - 点击"开始推理"按钮
   - 等待推理完成（通常需要几秒钟）

6. **查看结果**
   - 系统显示识别的药品信息
   - 提供详细的用药建议和注意事项

### 快捷键

- **← →**: 切换任务页面
- **Ctrl+Q**: 退出应用程序
- **F1**: 显示帮助信息

### 配置说明

#### 任务配置文件 (config/default_tasks.json)

```json
{
    "taskId": 1,
    "taskName": "感冒药识别",
    "guidanceText": "使用指引文本",
    "symptomPlaceholder": "症状输入提示",
    "medicineType": "感冒药",
    "expectedSymptoms": ["发热", "头痛", "咳嗽"],
    "modelPath": "models/cold_medicine_model.rknn",
    "configPath": "config/cold_medicine_config.json",
    "isActive": true
}
```

#### 模型接口

应用程序通过以下接口调用RKNN模型：

```bash
# 推理命令格式
./rknn_inference_demo model.rknn input.json output.json

# 输入JSON格式
{
    "image_path": "/path/to/image.jpg",
    "symptom_text": "用户症状描述",
    "task_type": "感冒药"
}

# 输出JSON格式
{
    "success": true,
    "detected_medicine": "药品名称",
    "medicine_image": "/path/to/cropped_image.jpg",
    "dosage_instructions": "用药说明",
    "confidence": 0.95
}
```

## 🔧 开发指南

### 添加新任务

1. **修改配置文件**
```json
{
    "taskId": 6,
    "taskName": "新药品识别",
    "medicineType": "新药品类型",
    "modelPath": "models/new_medicine_model.rknn"
}
```

2. **准备模型文件**
```bash
cp new_medicine_model.rknn build/models/
```

3. **重启应用程序**

### 自定义界面

修改 `main.cpp` 中的样式表：

```cpp
QString styleSheet = R"(
    /* 自定义样式 */
    QPushButton {
        background-color: #custom_color;
    }
)";
app.setStyleSheet(styleSheet);
```

### 扩展推理接口

实现 `ModelInference` 类的自定义推理逻辑：

```cpp
void ModelInference::startInference(const QString& imagePath, 
                                   const QString& symptomText, 
                                   const TaskData& taskData) {
    // 自定义推理实现
}
```

## 🐛 故障排除

### 常见问题

1. **编译失败**
```bash
# 检查Qt环境
qmake --version
# 检查依赖库
sudo apt install qtmultimedia5-dev
```

2. **运行时错误**
```bash
# 检查库路径
export LD_LIBRARY_PATH=/path/to/qt/lib:$LD_LIBRARY_PATH
# 检查依赖
ldd DiagnosisApp
```

3. **摄像头无法使用**
```bash
# 检查摄像头设备
ls /dev/video*
# 检查权限
sudo usermod -a -G video $USER
```

4. **模型推理失败**
```bash
# 检查模型文件
ls -la build/models/
# 检查推理程序
./rknn_inference_demo --help
```

### 日志调试

应用程序会在以下位置生成日志：

```bash
# 应用程序日志
build/logs/diagnosis_app.log

# Qt调试输出
export QT_LOGGING_RULES="*.debug=true"
./DiagnosisApp
```

## 📄 许可证

本项目基于MIT许可证开源。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📞 技术支持

如有问题，请联系：
- 邮箱：<EMAIL>
- 项目地址：https://github.com/medical-ai/diagnosis-app

---

**注意**: 本应用程序仅供参考，不能替代专业医疗建议。使用前请咨询医生。
