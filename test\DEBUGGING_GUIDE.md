# Qt编译问题调试指南

## 🚨 当前问题

错误信息：`unknown modules in Qt: core gui Core Gui Widgets Mutimedia Multimediawidgets`

## 🔍 调试步骤

### 1. 设置脚本权限并运行环境检查

```bash
cd test
chmod +x debug_qt_env.sh test_qmake.sh build.sh

# 运行完整的Qt环境检查
./debug_qt_env.sh
```

### 2. 测试qmake基本功能

```bash
# 运行简化的qmake测试
./test_qmake.sh
```

### 3. 检查Qt安装

```bash
# 检查已安装的Qt包
dpkg -l | grep qt5

# 检查Qt库文件
ls -la /usr/lib/aarch64-linux-gnu/libQt5*.so* | head -10
ls -la /usr/lib/libQt5*.so* | head -10
```

### 4. 安装缺失的Qt组件

```bash
# 更新包列表
sudo apt update

# 安装基础Qt开发包
sudo apt install qtbase5-dev qtchooser qt5-qmake qtbase5-dev-tools

# 安装多媒体模块
sudo apt install qtmultimedia5-dev libqt5multimedia5-plugins

# 对于某些系统，可能需要
sudo apt install qt5-default

# 设置Qt版本（如果有多个版本）
sudo qtchooser -install qt5 $(which qmake) $(which qmake | sed 's/qmake$//')
export QT_SELECT=qt5
```

## 🛠️ 常见问题解决

### 问题1：qmake未找到

**解决方案：**
```bash
# 查找qmake
find /usr -name "qmake*" 2>/dev/null

# 如果找到，创建软链接
sudo ln -sf /usr/lib/qt5/bin/qmake /usr/bin/qmake
```

### 问题2：Qt模块重复定义

**原因：** DiagnosisApp.pro中可能有重复的QT定义

**解决方案：** 已修复，确保只有一行：
```pro
QT += core gui widgets multimedia multimediawidgets
```

### 问题3：架构不匹配

**检查：**
```bash
# 检查系统架构
uname -m

# 检查Qt库架构
file /usr/lib/aarch64-linux-gnu/libQt5Core.so.5
```

### 问题4：权限问题

**解决方案：**
```bash
# 确保当前用户有权限访问Qt文件
ls -la /usr/lib/aarch64-linux-gnu/libQt5*.so*

# 如果需要，修复权限
sudo chmod 644 /usr/lib/aarch64-linux-gnu/libQt5*.so*
```

## 📋 手动验证清单

### ✅ 基础检查
- [ ] qmake命令可用：`which qmake`
- [ ] qmake版本正确：`qmake --version`
- [ ] Qt库文件存在：`ls /usr/lib/*/libQt5Core.so*`

### ✅ 模块检查
- [ ] QtCore：`pkg-config --exists Qt5Core`
- [ ] QtGui：`pkg-config --exists Qt5Gui`
- [ ] QtWidgets：`pkg-config --exists Qt5Widgets`
- [ ] QtMultimedia：`pkg-config --exists Qt5Multimedia`
- [ ] QtMultimediaWidgets：`pkg-config --exists Qt5MultimediaWidgets`

### ✅ 编译测试
- [ ] 简单项目qmake成功：`./test_qmake.sh`
- [ ] 实际项目qmake成功：`./build.sh`

## 🚀 快速修复命令

如果您想快速尝试修复，请按顺序执行：

```bash
# 1. 设置权限
chmod +x debug_qt_env.sh test_qmake.sh build.sh

# 2. 安装Qt开发包
sudo apt update
sudo apt install qtbase5-dev qtmultimedia5-dev qt5-qmake

# 3. 设置环境
export QT_SELECT=qt5

# 4. 测试qmake
./test_qmake.sh

# 5. 如果测试通过，尝试编译项目
./build.sh
```

## 📞 获取帮助

如果问题仍然存在，请运行以下命令并提供输出：

```bash
./debug_qt_env.sh > qt_debug.log 2>&1
./test_qmake.sh >> qt_debug.log 2>&1
cat qt_debug.log
```
