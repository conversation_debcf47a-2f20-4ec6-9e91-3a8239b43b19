#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QDebug>
#include <QFont>
#include <QFontDatabase>
#include "mainwindow.h"

/**
 * @brief 设置应用程序样式
 */
void setupApplicationStyle(QApplication& app) {
    // 设置应用程序样式
    app.setStyle(QStyleFactory::create("Fusion"));
    
    // 设置浅蓝色主题样式表
    QString styleSheet = R"(
        QMainWindow {
            background-color: #e6f3ff;
            color: #000000;
        }

        QWidget {
            background-color: #e6f3ff;
            color: #000000;
            font-family: "Microsoft YaHei", "SimHei", sans-serif;
            font-size: 12px;
        }

        QPushButton {
            background-color: #0078d4;
            border: 2px solid #005a9e;
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: bold;
            min-height: 30px;
            color: #ffffff;
        }

        QPushButton:hover {
            background-color: #106ebe;
            border-color: #004578;
        }

        QPushButton:pressed {
            background-color: #005a9e;
            border-color: #003d6b;
        }

        QPushButton:disabled {
            background-color: #cccccc;
            border-color: #999999;
            color: #666666;
        }

        QPushButton#primaryButton {
            background-color: #0078d4;
            border-color: #106ebe;
        }
        
        QPushButton#primaryButton:hover {
            background-color: #106ebe;
            border-color: #005a9e;
        }
        
        QPushButton#primaryButton:pressed {
            background-color: #005a9e;
        }
        
        QTextEdit, QPlainTextEdit {
            background-color: #ffffff;
            border: 2px solid #cccccc;
            border-radius: 6px;
            padding: 8px;
            color: #000000;
            selection-background-color: #0078d4;
        }

        QLineEdit {
            background-color: #ffffff;
            border: 2px solid #cccccc;
            border-radius: 6px;
            padding: 8px;
            color: #000000;
            min-height: 20px;
        }

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border-color: #0078d4;
        }

        QLabel {
            color: #000000;
            background-color: transparent;
        }

        QLabel#titleLabel {
            font-size: 18px;
            font-weight: bold;
            color: #0078d4;
        }

        QLabel#guidanceLabel {
            font-size: 16px;
            color: #000000;
            background-color: #ffffff;
            border: 1px solid #cccccc;
            border-radius: 6px;
            padding: 12px;
        }
        
        QScrollArea {
            background-color: #e6f3ff;
            border: none;
        }

        QScrollBar:vertical {
            background-color: #ffffff;
            width: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:vertical {
            background-color: #cccccc;
            border-radius: 6px;
            min-height: 20px;
        }

        QScrollBar::handle:vertical:hover {
            background-color: #0078d4;
        }

        QFrame {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            border-radius: 8px;
        }

        QFrame#imageFrame {
            background-color: #f8f9fa;
            border: 2px dashed #cccccc;
            border-radius: 8px;
        }

        QFrame#resultFrame {
            background-color: #ffffff;
            border: 2px solid #0078d4;
            border-radius: 8px;
        }

        QMessageBox {
            background-color: #ffffff;
            color: #000000;
        }

        QMessageBox QLabel {
            color: #000000;
        }

        QMessageBox QPushButton {
            background-color: #0078d4;
            color: #ffffff;
            border: 1px solid #005a9e;
            border-radius: 4px;
            padding: 6px 12px;
            min-width: 60px;
        }
        
        QTabWidget::pane {
            border: 1px solid #cccccc;
            background-color: #ffffff;
        }

        QTabBar::tab {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            padding: 8px 16px;
            margin-right: 2px;
            color: #000000;
        }

        QTabBar::tab:selected {
            background-color: #0078d4;
            border-color: #106ebe;
            color: #ffffff;
        }

        QTabBar::tab:hover {
            background-color: #e6f3ff;
        }

        QProgressBar {
            background-color: #ffffff;
            border: 2px solid #cccccc;
            border-radius: 6px;
            text-align: center;
            color: #000000;
        }

        QProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 4px;
        }
    )";
    
    app.setStyleSheet(styleSheet);
}

/**
 * @brief 创建应用程序目录
 */
void createAppDirectories() {
    QStringList dirs = {
        "data",
        "models",
        "images",
        "temp",
        "logs",
        "config"
    };
    
    for (const QString& dir : dirs) {
        QDir().mkpath(dir);
    }
}

/**
 * @brief 设置应用程序信息
 */
void setupApplicationInfo(QApplication& app) {
    app.setApplicationName("药盒识别与用药建议系统");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("Medical AI Solutions");
    app.setOrganizationDomain("medical-ai.com");
    
    // 设置应用程序图标（如果存在）
    QIcon appIcon(":/icons/app_icon.png");
    if (!appIcon.isNull()) {
        app.setWindowIcon(appIcon);
    }
}

/**
 * @brief 主函数
 */
int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 设置应用程序信息
    setupApplicationInfo(app);
    
    // 创建必要的目录
    createAppDirectories();
    
    // 设置应用程序样式
    setupApplicationStyle(app);
    
    // 设置字体
    QFont font("Microsoft YaHei", 10);
    app.setFont(font);
    
    // 创建并显示主窗口
    MainWindow window;
    window.show();
    
    qDebug() << "药盒识别与用药建议系统启动成功";
    qDebug() << "Qt版本:" << QT_VERSION_STR;
    qDebug() << "应用程序路径:" << QCoreApplication::applicationDirPath();
    
    return app.exec();
}
