#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QStackedWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QProgressBar>
#include <QStatusBar>
#include <QMenuBar>
#include <QAction>
#include <QTimer>
#include <QKeyEvent>
#include <QCloseEvent>
#include <QMessageBox>
#include <QDebug>

// 前向声明
class TaskPage;
class TaskManager;

/**
 * @brief 主窗口类
 * 管理多页任务界面，提供导航和状态显示功能
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void keyPressEvent(QKeyEvent *event) override;
    void closeEvent(QCloseEvent *event) override;

private slots:
    void onPreviousPage();
    void onNextPage();
    void onGoToPage(int pageIndex);
    void onQuestionAnswered(const QString& answer);
    void onTaskError(const QString& errorMessage);
    void onQuestionChanged(int currentIndex, int totalQuestions);
    void onAboutApplication();
    void onShowHelp();
    void onShowSettings();
    void updateStatusBar();

private:
    void setupUI();
    void setupMenuBar();
    void setupStatusBar();
    void setupNavigationBar();
    void setupCentralWidget();
    void connectSignals();
    void updateNavigationButtons();
    void updatePageIndicator();
    void loadTasks();
    void saveWindowState();
    void restoreWindowState();

    // UI组件
    QWidget *m_centralWidget;
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_navigationLayout;
    QStackedWidget *m_stackedWidget;
    
    // 导航组件
    QPushButton *m_previousButton;
    QPushButton *m_nextButton;
    QLabel *m_pageIndicator;
    QProgressBar *m_progressBar;
    
    // 菜单和状态栏
    QMenuBar *m_menuBar;
    QStatusBar *m_statusBar;
    QLabel *m_statusLabel;
    QLabel *m_timeLabel;
    
    // 菜单动作
    QAction *m_aboutAction;
    QAction *m_helpAction;
    QAction *m_settingsAction;
    QAction *m_exitAction;
    
    // 核心组件
    TaskManager *m_taskManager;
    QList<TaskPage*> m_taskPages;
    
    // 状态变量
    int m_currentPageIndex;
    int m_totalPages;
    QTimer *m_statusTimer;
    
    // 常量
    static const int WINDOW_MIN_WIDTH = 1024;
    static const int WINDOW_MIN_HEIGHT = 768;
    static const int NAVIGATION_BUTTON_WIDTH = 120;
    static const int NAVIGATION_BUTTON_HEIGHT = 40;
};

#endif // MAINWINDOW_H
