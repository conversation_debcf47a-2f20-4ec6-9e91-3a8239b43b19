#!/bin/bash
# Qt环境调试脚本
# 用于检查Qt安装和模块可用性

echo "=== Qt环境调试信息 ==="
echo "系统架构: $(uname -m)"
echo "系统版本: $(cat /etc/os-release | grep PRETTY_NAME)"
echo ""

echo "=== 查找qmake ==="
# 查找所有可能的qmake路径
QMAKE_PATHS=(
    "/usr/bin/qmake"
    "/usr/bin/qmake-qt5"
    "/usr/lib/qt5/bin/qmake"
    "/usr/lib/aarch64-linux-gnu/qt5/bin/qmake"
    "/opt/Qt5.15.2/5.15.2/gcc_64/bin/qmake"
    "/opt/Qt*/*/gcc_64/bin/qmake"
)

for qmake_path in "${QMAKE_PATHS[@]}"; do
    if [[ -f "$qmake_path" ]]; then
        echo "✓ 找到qmake: $qmake_path"
        echo "  版本: $($qmake_path --version 2>/dev/null | head -2)"
        echo "  Qt安装路径: $($qmake_path -query QT_INSTALL_PREFIX 2>/dev/null)"
        echo "  Qt库路径: $($qmake_path -query QT_INSTALL_LIBS 2>/dev/null)"
        echo ""
    fi
done

# 检查系统PATH中的qmake
if command -v qmake >/dev/null 2>&1; then
    echo "✓ 系统PATH中的qmake: $(which qmake)"
    echo "  版本: $(qmake --version 2>/dev/null | head -2)"
    echo ""
fi

echo "=== 查找Qt库文件 ==="
# 查找Qt库文件
QT_LIB_PATHS=(
    "/usr/lib"
    "/usr/lib/aarch64-linux-gnu"
    "/usr/lib/x86_64-linux-gnu"
    "/usr/local/lib"
    "/opt/Qt5.15.2/5.15.2/gcc_64/lib"
)

QT_MODULES=("Core" "Gui" "Widgets" "Multimedia" "MultimediaWidgets")

for lib_path in "${QT_LIB_PATHS[@]}"; do
    if [[ -d "$lib_path" ]]; then
        echo "检查路径: $lib_path"
        for module in "${QT_MODULES[@]}"; do
            lib_file="$lib_path/libQt5${module}.so"
            if [[ -f "$lib_file" ]]; then
                echo "  ✓ libQt5${module}.so"
            else
                echo "  ✗ libQt5${module}.so (未找到)"
            fi
        done
        echo ""
    fi
done

echo "=== 检查Qt开发包 ==="
# 检查Qt开发包是否安装
dpkg -l | grep -E "qt5|qtbase|qtmultimedia" | head -10

echo ""
echo "=== 检查pkg-config ==="
# 检查pkg-config
if command -v pkg-config >/dev/null 2>&1; then
    echo "✓ pkg-config可用"
    echo "Qt5Core: $(pkg-config --exists Qt5Core && echo '✓' || echo '✗')"
    echo "Qt5Gui: $(pkg-config --exists Qt5Gui && echo '✓' || echo '✗')"
    echo "Qt5Widgets: $(pkg-config --exists Qt5Widgets && echo '✓' || echo '✗')"
    echo "Qt5Multimedia: $(pkg-config --exists Qt5Multimedia && echo '✓' || echo '✗')"
    echo "Qt5MultimediaWidgets: $(pkg-config --exists Qt5MultimediaWidgets && echo '✓' || echo '✗')"
else
    echo "✗ pkg-config不可用"
fi

echo ""
echo "=== 环境变量 ==="
echo "PATH: $PATH"
echo "LD_LIBRARY_PATH: $LD_LIBRARY_PATH"
echo "QT_SELECT: $QT_SELECT"
echo "QTDIR: $QTDIR"

echo ""
echo "=== 建议的安装命令 ==="
echo "如果缺少Qt模块，请尝试以下命令："
echo "sudo apt update"
echo "sudo apt install qtbase5-dev qtchooser qt5-qmake qtbase5-dev-tools"
echo "sudo apt install qtmultimedia5-dev libqt5multimedia5-plugins"
echo "sudo apt install qt5-default  # 如果可用"
