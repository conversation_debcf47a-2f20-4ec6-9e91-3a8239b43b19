/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 5.12.6
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

static const unsigned char qt_resource_data[] = {
  // /mnt/e/code/diagnosis/config/default_tasks.json
  0x0,0x0,0x4,0x5b,
  0x0,
  0x0,0x10,0xe8,0x78,0x9c,0xd5,0x97,0xdb,0x4f,0x1a,0x59,0x1c,0xc7,0xdf,0xfd,0x2b,
  0xc8,0x3c,0x5b,0x56,0xb4,0x4d,0x36,0x7d,0xdb,0xec,0x53,0x1f,0x76,0xd3,0x64,0x7d,
  0x5b,0x9a,0x66,0x2,0x63,0x9d,0xa,0x83,0x81,0xa9,0xa9,0x69,0x9a,0xc8,0xc0,0x70,
  0xbf,0x29,0x15,0x23,0xba,0xb5,0x5e,0xa8,0x66,0xad,0x40,0xd4,0x22,0xa,0x94,0x64,
  0xff,0x95,0xce,0xb9,0xf0,0xe4,0xbf,0xb0,0xe7,0xc2,0xd0,0x11,0x13,0x77,0x24,0x9a,
  0x8d,0x84,0x87,0x73,0x7e,0xe7,0x77,0xf9,0xce,0x8f,0xf3,0xc9,0x6f,0x78,0x37,0xe6,
  0x20,0x1f,0x61,0x41,0xa,0x86,0xe4,0x80,0x22,0x3c,0x75,0x8,0x2e,0xe7,0x84,0x30,
  0xce,0xad,0x3e,0x31,0xa4,0xfe,0x16,0xf0,0xca,0x33,0xb2,0xe4,0xa5,0x47,0x93,0x13,
  0x93,0x8f,0x1f,0xb9,0x26,0x1f,0x4d,0xb8,0xa6,0x5d,0x13,0x4f,0x27,0xe8,0xd7,0x74,
  0x55,0xc5,0xd0,0x5c,0x88,0xf8,0xfc,0xc9,0xb6,0xf4,0xf3,0x6e,0xb0,0x1a,0x38,0x3c,
  0xa3,0x59,0x5c,0xe3,0xd7,0xf,0x7e,0x17,0xfd,0x12,0x2d,0x0,0xa3,0x5b,0x20,0xb6,
  0x82,0xb3,0x35,0x5c,0x8b,0x81,0xc4,0xa1,0x30,0xe4,0xfa,0xea,0x8d,0xec,0x15,0x15,
  0x8f,0x34,0x2d,0xbd,0x55,0x99,0xfb,0x97,0x1d,0xdc,0xcd,0x19,0x9d,0x2e,0xfa,0x70,
  0x30,0x14,0xa,0x52,0x5b,0x38,0xd2,0xb9,0x6c,0x87,0xdd,0x8a,0x5b,0xc1,0xb5,0x33,
  0x98,0x49,0x22,0x7d,0xdf,0x68,0x55,0x8c,0x66,0x1a,0x1e,0x55,0x7a,0x7f,0xef,0xc1,
  0x62,0xd6,0xe8,0x6c,0x5e,0xb6,0xcb,0x6e,0xc5,0xe5,0x74,0xc0,0x74,0x16,0x2e,0x47,
  0x61,0xa2,0x64,0x34,0x53,0x46,0xfb,0x13,0x6c,0xea,0x70,0xbd,0x8e,0xca,0xd1,0x41,
  0x5a,0x90,0xd1,0xf1,0xae,0x4e,0x72,0xa0,0x64,0xdc,0xad,0x4c,0x3a,0x1d,0x60,0xf3,
  0x80,0xe6,0x2a,0x9d,0xe3,0x6f,0x45,0xa0,0x57,0xa0,0x76,0x40,0xdc,0xd1,0x5a,0x1c,
  0xa5,0x1a,0x30,0x9f,0xc7,0xdd,0xba,0x5b,0x99,0x72,0x3a,0x90,0x76,0xe,0xe2,0x2d,
  0x1,0xb4,0x97,0xc0,0x7e,0x1a,0xe6,0xe,0x50,0x21,0x26,0x10,0x2d,0xbd,0x95,0x2a,
  0xce,0x9d,0x81,0x7c,0x89,0x28,0xa7,0xc9,0x5b,0x17,0xb8,0x5a,0xa5,0x52,0xe1,0x9,
  0x79,0x92,0x3c,0x51,0x45,0x34,0xa3,0xed,0xaa,0xd1,0xfd,0x8b,0x97,0xe4,0x82,0x2e,
  0xdb,0x19,0xe2,0x8d,0x36,0x56,0xb8,0x1a,0x50,0xcd,0xc0,0xd5,0x53,0x90,0xaf,0xe1,
  0xfd,0xf0,0xf7,0x25,0x6d,0xb8,0x5d,0xa1,0x45,0xff,0xbc,0x1a,0xf0,0x3f,0xf7,0x89,
  0x1e,0x69,0x36,0xe0,0xf3,0x4a,0x41,0xda,0x34,0x92,0x18,0xd7,0x3e,0xa3,0x56,0x8c,
  0xab,0xe4,0xba,0xf9,0x63,0x72,0xf5,0xa4,0x88,0xf1,0x2d,0xd,0x3e,0x6b,0x44,0x5,
  0xc8,0x2f,0xa3,0xc8,0xd1,0xd4,0xcf,0xce,0x27,0xff,0xd4,0x7f,0xfd,0xbe,0x14,0x6,
  0x7b,0xa7,0x68,0x6d,0x83,0x2e,0x56,0x4e,0xc0,0x5a,0x7,0x6e,0x26,0xd1,0x5a,0x9d,
  0x6c,0xe1,0xd7,0x70,0xaf,0xdd,0x82,0x8d,0x55,0x74,0x94,0x74,0x3a,0x9d,0xc3,0x4a,
  0xfc,0x92,0x57,0xf6,0xc8,0x8a,0x34,0xbd,0x38,0x7f,0xf5,0x77,0x1e,0x76,0x94,0xde,
  0xce,0x4b,0x1e,0x55,0xf2,0xfe,0xc1,0xa5,0xb3,0x1b,0x25,0x70,0x11,0xc2,0xb8,0x43,
  0xe0,0xe5,0xd9,0x8a,0xd5,0xa7,0xab,0x41,0x69,0x66,0x2e,0x25,0xc1,0xca,0x7a,0xdf,
  0xc7,0x38,0xcf,0x83,0x14,0x5b,0x11,0x7,0xb0,0xfd,0x91,0x79,0x27,0x8b,0xa0,0x74,
  0x6,0xca,0x79,0xe1,0xc5,0xb0,0xc6,0x80,0x57,0xf2,0x3d,0x17,0xd5,0x59,0x2a,0x90,
  0x6d,0x42,0x3f,0x79,0x48,0xd7,0x5e,0x9a,0xe2,0x5f,0x32,0xa3,0x33,0x38,0xa7,0x28,
  0xc3,0xb2,0x3d,0x1,0x65,0x46,0x7e,0x65,0x6,0xf3,0xdd,0x50,0x30,0x37,0x3a,0x5f,
  0x87,0x2,0xd7,0xa2,0xe5,0xd0,0x2f,0x1e,0x55,0x5e,0xa0,0x9d,0x51,0x83,0x6f,0xa4,
  0xc1,0xe1,0xfb,0xf1,0xff,0x84,0x69,0xf2,0x26,0x98,0x8e,0x76,0x48,0x23,0x46,0x83,
  0xe9,0x6a,0xe8,0x9d,0xc1,0x64,0xa6,0xb5,0x9,0x53,0xa9,0x4d,0xdc,0x1f,0x2a,0x52,
  0x56,0xf5,0x57,0x90,0x62,0x77,0x18,0x24,0xf7,0x51,0x24,0x41,0xc8,0x41,0xc9,0xf5,
  0x3e,0x51,0xfa,0x9,0x4e,0x69,0x7c,0x8d,0xb5,0xc,0xd6,0x92,0x3d,0xbd,0x49,0x33,
  0xd8,0x23,0xca,0x6c,0xad,0x3d,0xa2,0x6,0x1c,0xf1,0xf2,0xc,0x1d,0xb3,0x3e,0xdd,
  0x70,0x1,0xa6,0x4f,0x2b,0x6f,0x9a,0xf5,0xba,0x69,0xab,0x7c,0xec,0x9b,0xed,0x70,
  0x34,0x2f,0xca,0xca,0x9c,0xec,0xf3,0x49,0xc1,0xdb,0x42,0x64,0x89,0xbc,0x1f,0x82,
  0xa6,0x6e,0x22,0xa8,0x91,0x40,0x5a,0x6e,0x34,0x82,0xae,0x86,0xde,0x19,0x41,0x66,
  0x5a,0x9b,0x4,0x69,0x39,0x7a,0x1,0x1f,0x2a,0x41,0x16,0xf5,0x56,0x82,0x8c,0xf6,
  0x1e,0xc8,0xef,0xd2,0xa9,0xa0,0xe5,0x28,0x41,0x17,0x3b,0x58,0xeb,0x92,0x9,0xc1,
  0xd9,0x21,0xb3,0x5,0x6e,0x15,0xf9,0xcc,0xb0,0xc9,0x8e,0xd9,0x54,0xdb,0xd3,0x48,
  0xcb,0x31,0x6,0x58,0x61,0xb6,0x62,0xac,0x33,0x8a,0x6,0xb3,0x8a,0xeb,0x60,0xb6,
  0x4c,0x9,0x47,0xd9,0x8a,0x2b,0x87,0xd1,0x4,0x28,0x24,0xa0,0xbe,0x63,0x8b,0x1d,
  0x51,0x51,0x65,0x59,0x99,0xf1,0x89,0x7e,0xbf,0xa8,0x6,0x82,0x8b,0xb7,0x25,0xe8,
  0x5a,0xfc,0xfd,0x70,0xf4,0xf8,0x6,0x8e,0x70,0x24,0x32,0x12,0x44,0xd6,0xb8,0xbb,
  0x22,0x88,0xe7,0xb4,0x87,0xf,0xf1,0xed,0x45,0xe,0x1e,0x2a,0x3e,0x56,0xf5,0x56,
  0x7c,0x88,0xbd,0x3f,0x65,0x48,0x2b,0x22,0x4b,0x14,0x99,0x46,0x82,0xdc,0x51,0xa3,
  0x99,0xc5,0xc9,0x1a,0xb7,0x93,0xd1,0x83,0xbb,0x71,0xb0,0x57,0xa6,0xa7,0xe1,0x6,
  0xe8,0x46,0xc0,0xf2,0x2a,0x28,0x14,0xec,0x1,0xc5,0x7b,0x6c,0x8b,0x26,0x2e,0x86,
  0xcd,0x16,0xa6,0x86,0x91,0x63,0x91,0xd3,0x3f,0x21,0x7a,0xd8,0x9,0x93,0xc2,0x98,
  0x62,0x72,0xfa,0xa7,0x28,0xdc,0xee,0x43,0xd7,0xdb,0xdd,0x82,0x5f,0x8e,0x49,0x28,
  0xcc,0xd4,0x6c,0xd1,0x15,0x22,0x4a,0x44,0xcf,0xec,0xc8,0x2f,0x79,0xd7,0xe2,0xef,
  0x87,0xae,0x27,0x37,0xd0,0x85,0x5a,0xa7,0xe8,0xc3,0x16,0x3a,0xfd,0x74,0x7b,0xc0,
  0x86,0x42,0xef,0x8a,0xb1,0x41,0x5a,0x9b,0x98,0x5d,0x1c,0x1a,0x9d,0x22,0xb9,0xa5,
  0x20,0xf6,0xf5,0x16,0x98,0xe1,0xed,0xa,0xd0,0xf5,0xff,0x1f,0x33,0x8b,0x7a,0x2b,
  0x66,0xa8,0x74,0xc,0x52,0x27,0xfc,0x3f,0x8,0x7b,0xbd,0xcb,0xa2,0x12,0xe9,0xf0,
  0x86,0xd1,0xc9,0x91,0x16,0x50,0xcc,0x4e,0xf,0xf1,0x36,0xc5,0xf,0x17,0xc8,0x83,
  0xb4,0xf8,0x7d,0xb7,0x7,0xd8,0xa0,0xc1,0xb6,0x18,0xe3,0x4a,0xf8,0x3b,0xde,0xf,
  0x11,0x8c,0x1d,0x4b,0x65,0xb6,0x67,0x92,0xd8,0x44,0x2b,0x57,0xb1,0xb6,0x7,0xce,
  0x8f,0x51,0xbc,0xc2,0x4e,0xaa,0x75,0xd0,0x8d,0xd1,0xc8,0x66,0xba,0xb7,0x9e,0x1d,
  0x89,0xb5,0x5,0x59,0x15,0xfd,0xb2,0x72,0x5b,0xc4,0xcc,0xb0,0x51,0xc8,0x62,0xab,
  0x17,0x63,0xef,0xc7,0xfe,0x5,0xe8,0x5,0xf,0xa5,
  
};

static const unsigned char qt_resource_name[] = {
  // config
  0x0,0x6,
  0x6,0xa6,0x4c,0xf7,
  0x0,0x63,
  0x0,0x6f,0x0,0x6e,0x0,0x66,0x0,0x69,0x0,0x67,
    // default_tasks.json
  0x0,0x12,
  0xf,0xc7,0x6c,0x3e,
  0x0,0x64,
  0x0,0x65,0x0,0x66,0x0,0x61,0x0,0x75,0x0,0x6c,0x0,0x74,0x0,0x5f,0x0,0x74,0x0,0x61,0x0,0x73,0x0,0x6b,0x0,0x73,0x0,0x2e,0x0,0x6a,0x0,0x73,0x0,0x6f,
  0x0,0x6e,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/config
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x2,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/config/default_tasks.json
  0x0,0x0,0x0,0x12,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x97,0x5d,0xa6,0x10,0xca,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#ifdef QT_NAMESPACE
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_DiagnosisApp)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_DiagnosisApp)()
{
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (0x2, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_DiagnosisApp)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_DiagnosisApp)()
{
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (0x2, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_DiagnosisApp)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_DiagnosisApp)(); }
   } dummy;
}
